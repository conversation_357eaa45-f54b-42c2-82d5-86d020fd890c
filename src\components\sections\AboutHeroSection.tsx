"use client";

import { motion } from "framer-motion";
import { Trophy, Users, Target, Heart } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const AboutHeroSection: React.FC = () => {
  const values = [
    {
      icon: Trophy,
      title: "Excellence",
      description: "We strive for excellence in everything we do, from our platform to our customer service.",
    },
    {
      icon: Users,
      title: "Community",
      description: "Building strong communities through meaningful contests and celebrations.",
    },
    {
      icon: Target,
      title: "Innovation",
      description: "Constantly pushing boundaries to create the best contest platform experience.",
    },
    {
      icon: Heart,
      title: "Passion",
      description: "We're passionate about helping people celebrate achievements and recognize talent.",
    },
  ];

  return (
    <AnimatedSection
      variant="primary"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
      data-aos="fade-up"
      data-aos-duration="800"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-secondary-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-primary-500 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="text-center max-w-4xl mx-auto mb-16"
        >
          <motion.h1
            variants={staggerItemVariants}
            className="text-5xl sm:text-6xl lg:text-7xl font-bold leading-tight mb-6"
          >
            <span className="bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent">
              Empowering
            </span>
            <br />
            <span className="text-text-light dark:text-text-dark">
              Every Contest
            </span>
          </motion.h1>

          <motion.p
            variants={staggerItemVariants}
            className="text-xl lg:text-2xl text-gray-600 dark:text-gray-400 mb-8 leading-relaxed"
          >
            We believe that every achievement deserves recognition and every contest 
            should be a celebration of human potential and creativity.
          </motion.p>

          <motion.div
            variants={staggerItemVariants}
            className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm text-primary-700 dark:text-primary-300 px-6 py-3 rounded-full"
          >
            <Trophy className="h-5 w-5" />
            <span className="font-medium">Trusted by 50,000+ organizers worldwide</span>
          </motion.div>
        </motion.div>

        {/* Values Grid */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {values.map((value, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
              className="text-center group"
              whileHover={{ scale: 1.05 }}
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 backdrop-blur-sm rounded-2xl mb-4 group-hover:bg-white/20 transition-colors duration-300">
                <value.icon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
              </div>
              <h3 className="text-xl font-bold text-text-light dark:text-text-dark mb-2">
                {value.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                {value.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default AboutHeroSection;
