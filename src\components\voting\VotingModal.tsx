"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Heart,
  Plus,
  Minus,
  CreditCard,
  User,
  Mail,
  Phone,
  DollarSign,
  Vote,
  Star,
  Trophy
} from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Card from "@/components/ui/Card";

interface VotingModalProps {
  isOpen: boolean;
  onClose: () => void;
  contestant: {
    _id: string;
    name: string;
    stageName: string;
    profilePictureUrl: string;
    voteCount: number;
  };
  contest: {
    _id: string;
    title: string;
    votePrice: number;
  };
  onSuccess: (voteData: any) => void;
}

interface VoterInfo {
  name: string;
  email: string;
  phone: string;
}

export default function VotingModal({
  isOpen,
  onClose,
  contestant,
  contest,
  onSuccess
}: VotingModalProps) {
  const [step, setStep] = useState<'vote' | 'payment' | 'processing'>('vote');
  const [voteQuantity, setVoteQuantity] = useState(1);
  const [voterInfo, setVoterInfo] = useState<VoterInfo>({
    name: '',
    email: '',
    phone: ''
  });
  const [errors, setErrors] = useState<Partial<VoterInfo>>({});
  const [isLoading, setIsLoading] = useState(false);

  const totalAmount = voteQuantity * contest.votePrice;

  const validateVoterInfo = (): boolean => {
    const newErrors: Partial<VoterInfo> = {};

    if (!voterInfo.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (voterInfo.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (!voterInfo.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(voterInfo.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!voterInfo.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^(\+234|0)[789]\d{9}$/.test(voterInfo.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid Nigerian phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof VoterInfo, value: string) => {
    setVoterInfo(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleVoteQuantityChange = (change: number) => {
    const newQuantity = Math.max(1, Math.min(100, voteQuantity + change));
    setVoteQuantity(newQuantity);
  };

  const handleDirectQuantityChange = (value: string) => {
    const quantity = parseInt(value) || 1;
    setVoteQuantity(Math.max(1, Math.min(100, quantity)));
  };

  const handleProceedToPayment = () => {
    if (validateVoterInfo()) {
      setStep('payment');
    }
  };

  const handlePayment = async () => {
    setIsLoading(true);
    setStep('processing');

    try {
      // Initialize Paystack payment
      const paymentData = {
        email: voterInfo.email,
        amount: totalAmount * 100, // Paystack expects amount in kobo
        currency: 'NGN',
        reference: `vote_${contestant._id}_${Date.now()}`,
        metadata: {
          contestantId: contestant._id,
          contestId: contest._id,
          voteQuantity,
          voterName: voterInfo.name,
          voterPhone: voterInfo.phone
        }
      };

      // In a real implementation, you would integrate with Paystack here
      // For now, we'll simulate the payment process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate successful payment
      const voteResult = {
        reference: paymentData.reference,
        amount: totalAmount,
        quantity: voteQuantity,
        contestant: contestant,
        voter: voterInfo,
        timestamp: new Date().toISOString()
      };

      onSuccess(voteResult);
      onClose();
    } catch (error) {
      console.error('Payment failed:', error);
      setStep('payment');
    } finally {
      setIsLoading(false);
    }
  };

  const resetModal = () => {
    setStep('vote');
    setVoteQuantity(1);
    setVoterInfo({ name: '', email: '', phone: '' });
    setErrors({});
    setIsLoading(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          <Card className="p-0">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full">
                  <Heart className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    Vote for {contestant.stageName}
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {contest.title}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6">
              {step === 'vote' && (
                <div className="space-y-6">
                  {/* Contestant Info */}
                  <div className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <img
                      src={contestant.profilePictureUrl || "/api/placeholder/80/80"}
                      alt={contestant.name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {contestant.stageName}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {contestant.name}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Heart className="h-4 w-4 text-red-500" />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {contestant.voteCount} votes
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Vote Quantity */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Number of Votes
                    </label>
                    <div className="flex items-center space-x-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleVoteQuantityChange(-1)}
                        disabled={voteQuantity <= 1}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      
                      <Input
                        type="number"
                        value={voteQuantity}
                        onChange={(e) => handleDirectQuantityChange(e.target.value)}
                        min="1"
                        max="100"
                        className="w-20 text-center"
                      />
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleVoteQuantityChange(1)}
                        disabled={voteQuantity >= 100}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Maximum 100 votes per transaction
                    </p>
                  </div>

                  {/* Cost Breakdown */}
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Vote Price:
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        ₦{contest.votePrice.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Quantity:
                      </span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {voteQuantity}
                      </span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
                      <div className="flex items-center justify-between">
                        <span className="font-semibold text-gray-900 dark:text-white">
                          Total Amount:
                        </span>
                        <span className="text-xl font-bold text-purple-600 dark:text-purple-400">
                          ₦{totalAmount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Voter Information */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      Voter Information
                    </h4>
                    
                    <Input
                      label="Full Name"
                      icon={User}
                      value={voterInfo.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      error={errors.name}
                      placeholder="Enter your full name"
                      required
                    />
                    
                    <Input
                      label="Email Address"
                      type="email"
                      icon={Mail}
                      value={voterInfo.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      error={errors.email}
                      placeholder="Enter your email address"
                      required
                    />
                    
                    <Input
                      label="Phone Number"
                      type="tel"
                      icon={Phone}
                      value={voterInfo.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      error={errors.phone}
                      placeholder="e.g., +234 ************"
                      required
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={handleClose}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="gradient"
                      onClick={handleProceedToPayment}
                      className="flex-1"
                      glow={true}
                    >
                      <CreditCard className="h-4 w-4 mr-2" />
                      Proceed to Payment
                    </Button>
                  </div>
                </div>
              )}

              {step === 'payment' && (
                <div className="space-y-6">
                  {/* Payment Summary */}
                  <div className="text-center">
                    <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mx-auto mb-4">
                      <CreditCard className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      Confirm Payment
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      You're about to vote for {contestant.stageName}
                    </p>
                  </div>

                  {/* Payment Details */}
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Contestant:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {contestant.stageName}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Votes:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {voteQuantity}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Voter:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {voterInfo.name}
                      </span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                      <div className="flex justify-between">
                        <span className="font-semibold text-gray-900 dark:text-white">Total:</span>
                        <span className="text-xl font-bold text-green-600 dark:text-green-400">
                          ₦{totalAmount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      You will be redirected to Paystack to complete your payment securely
                    </p>
                    <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                      <span>Secured by</span>
                      <span className="font-semibold text-green-600">Paystack</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setStep('vote')}
                      className="flex-1"
                    >
                      Back
                    </Button>
                    <Button
                      variant="gradient"
                      onClick={handlePayment}
                      className="flex-1"
                      isLoading={isLoading}
                      glow={true}
                    >
                      <DollarSign className="h-4 w-4 mr-2" />
                      Pay ₦{totalAmount.toLocaleString()}
                    </Button>
                  </div>
                </div>
              )}

              {step === 'processing' && (
                <div className="text-center py-8">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mx-auto mb-4 animate-pulse">
                    <Vote className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Processing Payment...
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Please wait while we process your vote
                  </p>
                  <div className="flex items-center justify-center space-x-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
