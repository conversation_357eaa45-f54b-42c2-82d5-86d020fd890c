"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Send, User, Mail, MessageSquare, Building, CheckCircle } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import Input from "@/components/ui/Input";
import Button from "@/components/ui/Button";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

interface FormData {
  name: string;
  email: string;
  company: string;
  subject: string;
  message: string;
}

const ContactFormSection: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    company: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

  const handleInputChange = (field: keyof FormData, value: string): void => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const subjects = [
    "General Inquiry",
    "Technical Support",
    "Sales Question",
    "Partnership",
    "Feature Request",
    "Bug Report",
    "Other",
  ];

  if (isSubmitted) {
    return (
      <AnimatedSection
        variant="default"
        size="lg"
        animation="fadeIn"
      >
        <div className="max-w-2xl mx-auto text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-success-100 dark:bg-success-900/30 rounded-full mb-6"
          >
            <CheckCircle className="h-10 w-10 text-success-600 dark:text-success-400" />
          </motion.div>
          
          <h2 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            Message Sent Successfully!
          </h2>

          <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
            Thank you for reaching out. We&apos;ll get back to you within 24 hours.
          </p>
          
          <Button
            onClick={() => {
              setIsSubmitted(false);
              setFormData({
                name: "",
                email: "",
                company: "",
                subject: "",
                message: "",
              });
            }}
            variant="outline"
          >
            Send Another Message
          </Button>
        </div>
      </AnimatedSection>
    );
  }

  return (
    <AnimatedSection
      variant="light"
      size="xl"
      animation="fadeIn"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="200"
    >
      <SectionHeader>
        <SectionTitle gradient>Send Us a Message</SectionTitle>
        <SectionSubtitle>
          Fill out the form below and we&apos;ll get back to you as soon as possible.
        </SectionSubtitle>
      </SectionHeader>

      <div className="max-w-4xl mx-auto">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12"
        >
          {/* Contact Form */}
          <motion.div variants={staggerItemVariants}>
            <Card size="lg">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name */}
                <Input
                  label="Full Name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  leftIcon={<User className="h-5 w-5" />}
                  required
                  placeholder="Enter your full name"
                />

                {/* Email */}
                <Input
                  label="Email Address"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  leftIcon={<Mail className="h-5 w-5" />}
                  required
                  placeholder="Enter your email address"
                />

                {/* Company */}
                <Input
                  label="Company (Optional)"
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange("company", e.target.value)}
                  leftIcon={<Building className="h-5 w-5" />}
                  placeholder="Enter your company name"
                />

                {/* Subject */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Subject
                  </label>
                  <select
                    value={formData.subject}
                    onChange={(e) => handleInputChange("subject", e.target.value)}
                    required
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                  >
                    <option value="">Select a subject</option>
                    {subjects.map((subject) => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Message
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange("message", e.target.value)}
                    required
                    rows={6}
                    placeholder="Tell us how we can help you..."
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 resize-none"
                  />
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  size="lg"
                  fullWidth
                  isLoading={isSubmitting}
                  loadingText="Sending..."
                  rightIcon={<Send className="h-5 w-5" />}
                  motionProps={{
                    whileHover: { scale: 1.02 },
                    whileTap: { scale: 0.98 }
                  }}
                >
                  Send Message
                </Button>
              </form>
            </Card>
          </motion.div>

          {/* Contact Information */}
          <motion.div variants={staggerItemVariants} className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
                Let&apos;s Start a Conversation
              </h3>
              <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                Whether you&apos;re looking to create your first contest or need help with 
                an existing one, our team is here to support you every step of the way.
              </p>
            </div>

            {/* Quick Contact Cards */}
            <div className="space-y-4">
              <Card className="p-4 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                    <Mail className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <div className="font-medium text-slate-900 dark:text-slate-100">Email Us</div>
                    <div className="text-sm text-slate-600 dark:text-slate-300"><EMAIL></div>
                  </div>
                </div>
              </Card>

              <Card className="p-4 hover:shadow-md transition-shadow duration-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                    <MessageSquare className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <div className="font-medium text-slate-900 dark:text-slate-100">Live Chat</div>
                    <div className="text-sm text-slate-600 dark:text-slate-300">Available 24/7 on our website</div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Response Time */}
            <Card className="p-6 bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20">
              <h4 className="font-semibold text-text-light dark:text-text-dark mb-2">
                Quick Response Guaranteed
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                We typically respond to all inquiries within 2 hours during business hours, 
                and within 24 hours on weekends.
              </p>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default ContactFormSection;
