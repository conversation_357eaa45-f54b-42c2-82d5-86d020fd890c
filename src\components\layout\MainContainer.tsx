"use client";

import { ReactNode } from "react";
import { motion } from "framer-motion";
import Header from "./Header";
import Footer from "./Footer";

interface MainContainerProps {
  children: ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
  headerClassName?: string;
  footerClassName?: string;
  pageTitle?: string;
  pageDescription?: string;
}

const MainContainer: React.FC<MainContainerProps> = ({
  children,
  className = "",
  showHeader = true,
  showFooter = true,
  headerClassName = "",
  footerClassName = "",
  pageTitle,
  pageDescription,
}) => {
  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20,
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3,
        ease: "easeIn",
      },
    },
  };

  return (
    <div className="min-h-screen flex flex-col relative overflow-hidden bg-light-50 dark:bg-dark-50">
      {/* Modern Animated Background */}
      <div className="fixed inset-0 opacity-30 dark:opacity-40 pointer-events-none">
        <div className="absolute inset-0 grid-pattern" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-500/10 to-accent-500/5 dark:from-primary-500/20 dark:to-accent-500/10 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-accent-500/10 to-primary-500/5 dark:from-accent-500/20 dark:to-primary-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: "3s" }} />
      </div>

      {/* Header */}
      {showHeader && <Header className={headerClassName} />}

      {/* Main Content */}
      <motion.main
        variants={pageVariants as any}
        initial="initial"
        animate="animate"
        exit="exit"
        className={`flex-1 relative z-10 ${showHeader ? "pt-16 lg:pt-20" : ""} ${className}`}
      >
        {/* Page Header (if title/description provided) */}
        {(pageTitle || pageDescription) && (
          <section className="relative py-16 lg:py-24 overflow-hidden">
            {/* Modern Background Effects */}
            <div className="absolute inset-0 bg-gradient-to-br from-light-100 to-light-200 dark:from-dark-100 dark:to-dark-200" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(37,99,235,0.1)_0%,transparent_50%)] opacity-60" />
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(245,158,11,0.1)_0%,transparent_50%)] opacity-60" />

            <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-center max-w-4xl mx-auto"
              >
                {pageTitle && (
                  <h1 className="text-5xl lg:text-7xl font-bold font-display gradient-text-modern mb-6 tracking-tight">
                    {pageTitle}
                  </h1>
                )}
                {pageDescription && (
                  <p className="text-xl lg:text-2xl text-light-600 dark:text-dark-600 leading-relaxed max-w-3xl mx-auto">
                    {pageDescription}
                  </p>
                )}
              </motion.div>
            </div>
          </section>
        )}

        {/* Page Content */}
        <div className="flex-1">
          {children}
        </div>
      </motion.main>

      {/* Footer */}
      {showFooter && <Footer className={footerClassName} />}
    </div>
  );
};

export default MainContainer;
