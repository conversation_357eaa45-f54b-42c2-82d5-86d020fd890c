"use client";

import { useState, useMemo, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Search,
  Filter,
  Trophy,
  Users,
  Calendar,
  DollarSign,
  Clock,
  Eye,
  Star,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  AlertCircle
} from "lucide-react";
import MainContainer from "@/components/layout/MainContainer";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { ContestsService, Contest as ApiContest } from "@/lib/api/contests";

interface Contest {
  id: string;
  title: string;
  description: string;
  organizer: string;
  category: string;
  status: "active" | "upcoming" | "voting" | "completed";
  contestants: number;
  votes: number;
  prize: string;
  deadline: string;
  startDate: string;
  image: string;
  tags: string[];
  featured: boolean;
  entryFee?: string;
  ageLimit?: string;
  requirements: string[];
}

// Mock data - replace with API call
const mockContests: Contest[] = [
  {
    id: "1",
    title: "Face of Nigeria 2024",
    description: "Nigeria's premier beauty and talent contest celebrating the diversity, intelligence, and grace of Nigerian women. Contestants compete in various categories including beauty, talent, and intelligence.",
    organizer: "Miss Nigeria Organization",
    category: "Beauty & Talent",
    status: "active",
    contestants: 156,
    votes: 1234,
    prize: "₦2,000,000",
    deadline: "2024-12-31",
    startDate: "2024-11-01",
    image: "/api/placeholder/400/250",
    tags: ["Beauty", "Talent", "Nigeria"],
    featured: true,
    entryFee: "₦50,000",
    ageLimit: "18-26 years",
    requirements: ["Nigerian citizen", "Single/Never married", "No children", "Height: 5'6\" minimum"]
  },
  {
    id: "2",
    title: "Mr. Lagos 2024",
    description: "The ultimate male pageant celebrating the strength, intelligence, and charisma of Lagos men. Contestants compete in fitness, talent, and leadership categories.",
    organizer: "Lagos State Tourism Board",
    category: "Male Pageant",
    status: "active",
    contestants: 89,
    votes: 567,
    prize: "₦1,000,000",
    deadline: "2024-12-25",
    startDate: "2024-10-15",
    image: "/api/placeholder/400/250",
    tags: ["Male", "Pageant", "Lagos"],
    featured: false,
    entryFee: "₦30,000",
    ageLimit: "21-30 years",
    requirements: ["Lagos resident", "Single", "University graduate", "No criminal record"]
  },
  {
    id: "3",
    title: "Miss University Nigeria 2024",
    description: "A prestigious beauty pageant for female university students across Nigeria, celebrating academic excellence, beauty, and social impact.",
    organizer: "Nigerian Universities Commission",
    category: "Student Pageant",
    status: "voting",
    contestants: 234,
    votes: 2156,
    prize: "₦600,000",
    deadline: "2024-12-20",
    startDate: "2024-09-01",
    image: "/api/placeholder/400/250",
    tags: ["University", "Student", "Beauty"],
    featured: true,
    entryFee: "₦25,000",
    ageLimit: "18-25 years",
    requirements: ["Current university student", "CGPA 3.0 minimum", "Nigerian citizen", "Good moral standing"]
  },
  {
    id: "4",
    title: "Most Handsome Man in Abuja 2025",
    description: "A prestigious male beauty contest celebrating the most handsome and charismatic men in Nigeria's capital city.",
    organizer: "Abuja Entertainment Hub",
    category: "Male Beauty",
    status: "upcoming",
    contestants: 0,
    votes: 0,
    prize: "₦4,000,000",
    deadline: "2025-01-15",
    startDate: "2024-12-01",
    image: "/api/placeholder/400/250",
    tags: ["Male", "Beauty", "Abuja"],
    featured: false,
    entryFee: "₦40,000",
    ageLimit: "20-35 years",
    requirements: ["Abuja resident", "Single", "Height: 5'8\" minimum", "Professional photos required"]
  },
  {
    id: "5",
    title: "Miss Entrepreneur Nigeria 2024",
    description: "Celebrating successful female entrepreneurs and business leaders across Nigeria. Contestants showcase their business acumen and leadership skills.",
    organizer: "Women in Business Nigeria",
    category: "Business & Beauty",
    status: "active",
    contestants: 78,
    votes: 445,
    prize: "₦1,200,000",
    deadline: "2024-12-28",
    startDate: "2024-10-20",
    image: "/api/placeholder/400/250",
    tags: ["Business", "Entrepreneur", "Women"],
    featured: false,
    entryFee: "₦75,000",
    ageLimit: "25-40 years",
    requirements: ["Business owner", "Minimum 2 years in business", "Nigerian citizen", "Proven business success"]
  },
  {
    id: "6",
    title: "King of Comedy Nigeria 2024",
    description: "Nigeria's premier comedy talent contest for male comedians. Contestants compete in stand-up, skits, and entertainment categories.",
    organizer: "Nigerian Comedy Awards",
    category: "Comedy & Entertainment",
    status: "completed",
    contestants: 167,
    votes: 1890,
    prize: "₦800,000",
    deadline: "2024-11-30",
    startDate: "2024-09-15",
    image: "/api/placeholder/400/250",
    tags: ["Comedy", "Entertainment", "Talent"],
    featured: false,
    entryFee: "₦20,000",
    ageLimit: "18-45 years",
    requirements: ["Professional comedian", "Minimum 2 years experience", "Original content only", "Clean comedy preferred"]
  }
];

const categories = ["All", "Beauty & Talent", "Male Pageant", "Student Pageant", "Male Beauty", "Business & Beauty", "Comedy & Entertainment"];
const statuses = ["All", "Active", "Upcoming", "Voting", "Completed"];

// Helper function for status colors
function getStatusColor(status: string) {
  switch (status) {
    case "active":
      return "text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30";
    case "upcoming":
      return "text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/30";
    case "voting":
      return "text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/30";
    case "completed":
      return "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30";
    default:
      return "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30";
  }
}

// Helper function for date formatting
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric"
  });
}

export default function ContestsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedStatus, setSelectedStatus] = useState("All");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"deadline" | "participants" | "votes" | "prize">("deadline");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [contests, setContests] = useState<Contest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch contests from API
  useEffect(() => {
    const fetchContests = async () => {
      try {
        setLoading(true);
        setError(null);

        const contestsData = await ContestsService.getPublicContests({
          page: 1,
          limit: 50,
        });

        // Transform API data to match our interface
        const transformedContests: Contest[] = contestsData.map((apiContest: any) => ({
            id: apiContest._id,
            title: apiContest.title,
            description: apiContest.description,
            organizer: "Contest Organizer", // API doesn't provide organizer name directly
            category: "General", // You might want to add category to API
            status: apiContest.status === 'draft' ? 'upcoming' : 'active',
            contestants: 0, // You'll need to get this from contestants API
            votes: 0, // You'll need to get this from voting API
            prize: `₦${apiContest.votePrice?.toLocaleString() || '0'}`, // Use votePrice for prize
            deadline: apiContest.endsAt,
            startDate: apiContest.startsAt,
            image: apiContest.bannerUrl || "/api/placeholder/400/250",
            tags: [],
            featured: false,
            entryFee: `₦${apiContest.registrationFee?.toLocaleString() || '0'}`,
            requirements: []
          }));

        setContests(transformedContests);
      } catch (err: any) {
        console.error("Error fetching contests:", err);

        // Check if it's an authentication error
        if (err.status === 401 || err.message?.includes('token') || err.message?.includes('auth')) {
          setError("Please login to view contests. Showing sample contests for now.");
        } else {
          setError("Failed to load contests. Showing sample contests for now.");
        }

        // Fallback to mock data
        setContests(mockContests);
      } finally {
        setLoading(false);
      }
    };

    fetchContests();
  }, []);

  const filteredAndSortedContests = useMemo(() => {
    let filtered = contests.filter(contest => {
      const matchesSearch = contest.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           contest.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           contest.organizer.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === "All" || contest.category === selectedCategory;
      const matchesStatus = selectedStatus === "All" || contest.status === selectedStatus.toLowerCase();

      return matchesSearch && matchesCategory && matchesStatus;
    });

    // Sort contests
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case "deadline":
          aValue = new Date(a.deadline);
          bValue = new Date(b.deadline);
          break;
        case "participants":
          aValue = a.contestants;
          bValue = b.contestants;
          break;
        case "votes":
          aValue = a.votes;
          bValue = b.votes;
          break;
        case "prize":
          aValue = parseInt(a.prize.replace(/[$,]/g, ""));
          bValue = parseInt(b.prize.replace(/[$,]/g, ""));
          break;
        default:
          return 0;
      }
      
      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [searchTerm, selectedCategory, selectedStatus, sortBy, sortOrder]);



  return (
    <MainContainer
      pageTitle="Browse Contests"
      pageDescription="Discover amazing contests and competitions. Vote for your favorites and celebrate outstanding achievements."
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <Card className="mb-8">
          <div className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <Input
                  placeholder="Search contests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="w-4 h-4" />}
                />
              </div>
              
              {/* Category Filter */}
              <div className="w-full lg:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              
              {/* Status Filter */}
              <div className="w-full lg:w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500 focus:border-transparent"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* View Controls */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 mb-4 sm:mb-0">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {filteredAndSortedContests.length} contest{filteredAndSortedContests.length !== 1 ? 's' : ''} found
                </span>
              </div>

              <div className="flex items-center gap-4">
                {/* Sort Controls */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  >
                    <option value="deadline">Deadline</option>
                    <option value="participants">Contestants</option>
                    <option value="votes">Votes</option>
                    <option value="prize">Prize</option>
                  </select>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                    leftIcon={sortOrder === "asc" ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                  >
                    {sortOrder === "asc" ? "Ascending" : "Descending"}
                  </Button>
                </div>

                {/* View Mode Toggle */}
                <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                  <Button
                    variant={viewMode === "grid" ? "primary" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-none border-0"
                    leftIcon={<Grid3X3 className="w-4 h-4" />}
                  >
                    Grid
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "primary" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-none border-0"
                    leftIcon={<List className="w-4 h-4" />}
                  >
                    List
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card className="mb-8">
            <div className="p-6 text-center">
              <div className="text-red-500 mb-2">
                <AlertCircle className="w-12 h-12 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Error Loading Contests
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {error}
              </p>
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
              >
                Try Again
              </Button>
            </div>
          </Card>
        )}

        {/* Contest Grid/List */}
        {!loading && !error && filteredAndSortedContests.length === 0 ? (
          <Card className="text-center py-12">
            <div className="max-w-md mx-auto">
              <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No contests found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Try adjusting your search criteria or filters to find contests.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("All");
                  setSelectedStatus("All");
                }}
              >
                Clear Filters
              </Button>
            </div>
          </Card>
        ) : !loading && !error ? (
          <div className={viewMode === "grid"
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
          }>
            {filteredAndSortedContests.map((contest, index) => (
              <motion.div
                key={contest.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                {viewMode === "grid" ? (
                  <ContestCard contest={contest} />
                ) : (
                  <ContestListItem contest={contest} />
                )}
              </motion.div>
            ))}
          </div>
        ) : null}
      </div>
    </MainContainer>
  );
}

// Contest Card Component for Grid View
function ContestCard({ contest }: { contest: Contest }) {
  return (
    <Card hover interactive className="h-full">
      <div className="relative">
        {/* Contest Image */}
        <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-t-xl overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <Trophy className="w-12 h-12 text-gray-400" />
          </div>
        </div>

        {/* Featured Badge */}
        {contest.featured && (
          <div className="absolute top-3 left-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </span>
          </div>
        )}

        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(contest.status)}`}>
            {contest.status}
          </span>
        </div>
      </div>

      <div className="p-6">
        {/* Contest Info */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
            {contest.title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            by {contest.organizer}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
            {contest.description}
          </p>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-4">
          {contest.tags.slice(0, 3).map(tag => (
            <span
              key={tag}
              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <Users className="w-4 h-4 mr-1" />
            {contest.contestants} contestants
          </div>
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <Eye className="w-4 h-4 mr-1" />
            {contest.votes} votes
          </div>
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <DollarSign className="w-4 h-4 mr-1" />
            {contest.prize} prize
          </div>
          <div className="flex items-center text-gray-600 dark:text-gray-400">
            <Calendar className="w-4 h-4 mr-1" />
            {formatDate(contest.deadline)}
          </div>
        </div>

        {/* Action Button */}
        <Button
          fullWidth
          variant="gradient"
          leftIcon={<Eye className="w-4 h-4" />}
          onClick={() => window.location.href = `/contests/${contest.id}`}
          glow={true}
          motionProps={{
            whileHover: { scale: 1.02, y: -2 },
            whileTap: { scale: 0.98 }
          }}
        >
          View Contest
        </Button>
      </div>
    </Card>
  );
}

// Contest List Item Component for List View
function ContestListItem({ contest }: { contest: Contest }) {
  return (
    <Card hover interactive>
      <div className="p-6">
        <div className="flex flex-col lg:flex-row lg:items-center gap-4">
          {/* Contest Image */}
          <div className="w-full lg:w-32 h-20 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-lg overflow-hidden flex-shrink-0">
            <div className="w-full h-full flex items-center justify-center">
              <Trophy className="w-8 h-8 text-gray-400" />
            </div>
          </div>

          {/* Contest Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1">
                  {contest.title}
                  {contest.featured && (
                    <Star className="w-4 h-4 text-yellow-500 inline ml-2" />
                  )}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  by {contest.organizer}
                </p>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(contest.status)}`}>
                {contest.status}
              </span>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
              {contest.description}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-1 mb-3">
              {contest.tags.slice(0, 4).map(tag => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* Stats */}
            <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-1" />
                {contest.contestants} contestants
              </div>
              <div className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                {contest.votes} votes
              </div>
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 mr-1" />
                {contest.prize} prize
              </div>
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                Ends {formatDate(contest.deadline)}
              </div>
            </div>
          </div>

          {/* Action Button */}
          <div className="flex-shrink-0">
            <Button
              variant="gradient"
              leftIcon={<Eye className="w-4 h-4" />}
              onClick={() => window.location.href = `/contests/${contest.id}`}
              glow={true}
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 }
              }}
            >
              View Contest
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}
