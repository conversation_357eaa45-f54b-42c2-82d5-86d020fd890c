"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  User,
  Mail,
  Phone,
  Upload,
  FileText,
  CreditCard,
  UserPlus,
  Camera,
  MapPin,
  Calendar,
  DollarSign,
  CheckCircle
} from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Card from "@/components/ui/Card";
import { ContestantsService } from "@/lib/api/contestants";

interface ContestantRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  contest: {
    _id: string;
    title: string;
    registrationFee: number;
    requireApproval: boolean;
  };
  onSuccess: (registrationData: any) => void;
}

interface ContestantData {
  name: string;
  stageName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  address: string;
  bio: string;
  profilePicture: File | null;
}

export default function ContestantRegistrationModal({
  isOpen,
  onClose,
  contest,
  onSuccess
}: ContestantRegistrationModalProps) {
  const [step, setStep] = useState<'info' | 'payment' | 'processing' | 'success'>('info');
  const [formData, setFormData] = useState<ContestantData>({
    name: '',
    stageName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    address: '',
    bio: '',
    profilePicture: null
  });
  const [errors, setErrors] = useState<Partial<ContestantData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const validateForm = (): boolean => {
    const newErrors: Partial<ContestantData> = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Full name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    // Stage name validation
    if (!formData.stageName.trim()) {
      newErrors.stageName = 'Stage name is required';
    } else if (formData.stageName.trim().length < 2) {
      newErrors.stageName = 'Stage name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^(\+234|0)[789]\d{9}$/.test(formData.phone.replace(/\s/g, ''))) {
      newErrors.phone = 'Please enter a valid Nigerian phone number';
    }

    // Date of birth validation
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = 'Date of birth is required';
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 18) {
        newErrors.dateOfBirth = 'You must be at least 18 years old';
      }
    }

    // Address validation
    if (!formData.address.trim()) {
      newErrors.address = 'Address is required';
    }

    // Bio validation
    if (!formData.bio.trim()) {
      newErrors.bio = 'Bio is required';
    } else if (formData.bio.trim().length < 50) {
      newErrors.bio = 'Bio must be at least 50 characters';
    } else if (formData.bio.trim().length > 500) {
      newErrors.bio = 'Bio must not exceed 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ContestantData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setErrors(prev => ({ ...prev, profilePicture: 'Please select an image file' }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, profilePicture: 'Image size must be less than 5MB' }));
        return;
      }

      setFormData(prev => ({ ...prev, profilePicture: file }));
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Clear any previous errors
      if (errors.profilePicture) {
        setErrors(prev => ({ ...prev, profilePicture: undefined }));
      }
    }
  };

  const handleProceedToPayment = () => {
    if (validateForm()) {
      if (contest.registrationFee > 0) {
        setStep('payment');
      } else {
        handleSubmitRegistration();
      }
    }
  };

  const handleSubmitRegistration = async () => {
    setIsLoading(true);
    setStep('processing');

    try {
      // Create FormData for file upload
      const registrationData = new FormData();
      registrationData.append('contestId', contest._id);
      registrationData.append('name', formData.name);
      registrationData.append('stageName', formData.stageName);
      registrationData.append('email', formData.email);
      registrationData.append('phone', formData.phone);
      registrationData.append('dateOfBirth', formData.dateOfBirth);
      registrationData.append('address', formData.address);
      registrationData.append('bio', formData.bio);
      
      if (formData.profilePicture) {
        registrationData.append('profilePicture', formData.profilePicture);
      }

      // Submit registration
      const response = await ContestantsService.registerContestant(registrationData);

      if (response.success) {
        setStep('success');
        setTimeout(() => {
          onSuccess(response.contestant);
          handleClose();
        }, 3000);
      } else {
        throw new Error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setStep('info');
      setErrors({ name: error.message || 'Registration failed. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePayment = async () => {
    setIsLoading(true);

    try {
      // Initialize Paystack payment for registration fee
      const paymentData = {
        email: formData.email,
        amount: contest.registrationFee * 100, // Paystack expects amount in kobo
        currency: 'NGN',
        reference: `reg_${contest._id}_${Date.now()}`,
        metadata: {
          contestId: contest._id,
          contestantName: formData.name,
          contestantEmail: formData.email,
          type: 'registration'
        }
      };

      // Simulate payment process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // After successful payment, submit registration
      await handleSubmitRegistration();
    } catch (error) {
      console.error('Payment failed:', error);
      setStep('payment');
      setIsLoading(false);
    }
  };

  const resetModal = () => {
    setStep('info');
    setFormData({
      name: '',
      stageName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      address: '',
      bio: '',
      profilePicture: null
    });
    setErrors({});
    setPreviewImage(null);
    setIsLoading(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={handleClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          <Card className="p-0">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                  <UserPlus className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    Register for Contest
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {contest.title}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6">
              {step === 'info' && (
                <div className="space-y-6">
                  {/* Registration Fee Notice */}
                  {contest.registrationFee > 0 && (
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        <span className="font-medium text-blue-900 dark:text-blue-100">
                          Registration Fee: ₦{contest.registrationFee.toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        Payment will be required to complete your registration
                      </p>
                    </div>
                  )}

                  {/* Approval Notice */}
                  {contest.requireApproval && (
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                        <span className="font-medium text-yellow-900 dark:text-yellow-100">
                          Approval Required
                        </span>
                      </div>
                      <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        Your registration will be reviewed before approval
                      </p>
                    </div>
                  )}

                  {/* Personal Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Full Name"
                      icon={User}
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      error={errors.name}
                      placeholder="Enter your full name"
                      required
                    />
                    
                    <Input
                      label="Stage Name"
                      icon={User}
                      value={formData.stageName}
                      onChange={(e) => handleInputChange('stageName', e.target.value)}
                      error={errors.stageName}
                      placeholder="Enter your stage name"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Email Address"
                      type="email"
                      icon={Mail}
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      error={errors.email}
                      placeholder="Enter your email"
                      required
                    />
                    
                    <Input
                      label="Phone Number"
                      type="tel"
                      icon={Phone}
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      error={errors.phone}
                      placeholder="+234 ************"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Date of Birth"
                      type="date"
                      icon={Calendar}
                      value={formData.dateOfBirth}
                      onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                      error={errors.dateOfBirth}
                      required
                    />
                    
                    <Input
                      label="Address"
                      icon={MapPin}
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      error={errors.address}
                      placeholder="Enter your address"
                      required
                    />
                  </div>

                  {/* Bio */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bio <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      value={formData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      placeholder="Tell us about yourself (50-500 characters)"
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <div className="flex justify-between items-center mt-1">
                      {errors.bio && (
                        <p className="text-sm text-red-600 dark:text-red-400">{errors.bio}</p>
                      )}
                      <p className="text-xs text-gray-500 dark:text-gray-400 ml-auto">
                        {formData.bio.length}/500
                      </p>
                    </div>
                  </div>

                  {/* Profile Picture */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Profile Picture
                    </label>
                    <div className="flex items-center space-x-4">
                      {previewImage ? (
                        <img
                          src={previewImage}
                          alt="Preview"
                          className="w-20 h-20 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          <Camera className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                      <div className="flex-1">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleFileChange}
                          className="hidden"
                          id="profile-picture"
                        />
                        <label
                          htmlFor="profile-picture"
                          className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Choose Photo
                        </label>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Max 5MB, JPG/PNG only
                        </p>
                      </div>
                    </div>
                    {errors.profilePicture && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                        {errors.profilePicture}
                      </p>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={handleClose}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="gradient"
                      onClick={handleProceedToPayment}
                      className="flex-1"
                      glow={true}
                    >
                      {contest.registrationFee > 0 ? (
                        <>
                          <CreditCard className="h-4 w-4 mr-2" />
                          Proceed to Payment
                        </>
                      ) : (
                        <>
                          <UserPlus className="h-4 w-4 mr-2" />
                          Register Now
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {step === 'payment' && (
                <div className="space-y-6">
                  {/* Payment Summary */}
                  <div className="text-center">
                    <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mx-auto mb-4">
                      <CreditCard className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      Complete Registration
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Pay registration fee to complete your application
                    </p>
                  </div>

                  {/* Registration Summary */}
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Contest:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {contest.title}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Contestant:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {formData.stageName}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Email:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {formData.email}
                      </span>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                      <div className="flex justify-between">
                        <span className="font-semibold text-gray-900 dark:text-white">Registration Fee:</span>
                        <span className="text-xl font-bold text-green-600 dark:text-green-400">
                          ₦{contest.registrationFee.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      You will be redirected to Paystack to complete your payment securely
                    </p>
                    <div className="flex items-center justify-center space-x-2 text-xs text-gray-500">
                      <span>Secured by</span>
                      <span className="font-semibold text-green-600">Paystack</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setStep('info')}
                      className="flex-1"
                    >
                      Back
                    </Button>
                    <Button
                      variant="gradient"
                      onClick={handlePayment}
                      className="flex-1"
                      isLoading={isLoading}
                      glow={true}
                    >
                      <DollarSign className="h-4 w-4 mr-2" />
                      Pay ₦{contest.registrationFee.toLocaleString()}
                    </Button>
                  </div>
                </div>
              )}

              {step === 'processing' && (
                <div className="text-center py-8">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mb-4 animate-pulse">
                    <UserPlus className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Processing Registration...
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Please wait while we process your registration
                  </p>
                  <div className="flex items-center justify-center space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              )}

              {step === 'success' && (
                <div className="text-center py-8">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    Registration Successful!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {contest.requireApproval
                      ? "Your registration has been submitted and is pending approval."
                      : "You have been successfully registered for the contest!"
                    }
                  </p>
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <p className="text-sm text-green-700 dark:text-green-300">
                      A confirmation email has been sent to {formData.email}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
