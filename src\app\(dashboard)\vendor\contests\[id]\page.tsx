"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useParams } from "next/navigation";
import {
  ArrowLeft,
  Users,
  TrendingUp,
  DollarSign,
  Calendar,
  Edit,
  Settings,
  BarChart3,
  UserPlus,
  Trophy,
  Clock,
  Eye,
  Share2
} from "lucide-react";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { ContestsService, Contest } from "@/lib/api/contests";
import { handleApiError } from "@/lib/api";

export default function ContestOverviewPage() {
  const params = useParams();
  const contestId = params.id as string;
  const [contest, setContest] = useState<Contest | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContest = async () => {
      try {
        setLoading(true);
        setError(null);
        const contestData = await ContestsService.getContest(contestId);
        setContest(contestData);
      } catch (err: any) {
        console.error("Error fetching contest:", err);
        setError(handleApiError(err));
      } finally {
        setLoading(false);
      }
    };

    if (contestId) {
      fetchContest();
    }
  }, [contestId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "draft": return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      case "completed": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "paused": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Error loading contest</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{error}</p>
          <Link href="/vendor/contests" className="mt-4 inline-block">
            <Button variant="outline">Back to Contests</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!contest) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Contest not found</h1>
          <Link href="/vendor/contests" className="mt-4 inline-block">
            <Button variant="outline">Back to Contests</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/vendor/contests">
            <Button variant="ghost" size="sm" leftIcon={<ArrowLeft className="w-4 h-4" />}>
              Back to Contests
            </Button>
          </Link>
        </div>

        {/* Contest Header */}
        <Card className="overflow-hidden">
          {contest.bannerUrl && (
            <div className="h-64 bg-gradient-to-r from-primary-500 to-secondary-500 relative">
              <img 
                src={contest.bannerUrl} 
                alt={contest.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40"></div>
              <div className="absolute bottom-6 left-6 text-white">
                <div className="flex items-center gap-3 mb-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(contest.status)}`}>
                    {contest.status.charAt(0).toUpperCase() + contest.status.slice(1)}
                  </span>
                </div>
                <h1 className="text-3xl font-bold mb-2">{contest.title}</h1>
                <p className="text-gray-200 max-w-2xl">{contest.description}</p>
              </div>
            </div>
          )}
          
          <div className="p-6">
            <div className="flex flex-wrap gap-3">
              <Link href={`/vendor/contests/${contest._id}/edit`}>
                <Button variant="outline" leftIcon={<Edit className="w-4 h-4" />}>
                  Edit Contest
                </Button>
              </Link>
              <Link href={`/vendor/contests/${contest._id}/analytics`}>
                <Button variant="outline" leftIcon={<BarChart3 className="w-4 h-4" />}>
                  Analytics
                </Button>
              </Link>
              <Link href={`/vendor/contests/${contest._id}/contestants`}>
                <Button variant="outline" leftIcon={<Users className="w-4 h-4" />}>
                  Contestants
                </Button>
              </Link>
              <Link href={`/vendor/contests/${contest._id}/settings`}>
                <Button variant="outline" leftIcon={<Settings className="w-4 h-4" />}>
                  Settings
                </Button>
              </Link>
              <Button variant="outline" leftIcon={<Share2 className="w-4 h-4" />}>
                Share Contest
              </Button>
            </div>
          </div>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Contestants</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{contest.contestants || 0}</p>
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">Registered</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Votes</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{contest.votes || 0}</p>
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">Cast</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Registration Fee</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">₦{contest.registrationFee.toLocaleString()}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Per contestant</p>
              </div>
              <DollarSign className="w-8 h-8 text-orange-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Vote Price</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">₦{contest.votePrice.toLocaleString()}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Per vote</p>
              </div>
              <Eye className="w-8 h-8 text-purple-500" />
            </div>
          </Card>
        </div>

        {/* Contest Timeline */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Contest Timeline
            </h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Registration Opens</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(contest.registrationStartDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Registration Closes</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(contest.registrationCloseDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Voting Starts</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(contest.votingStartDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">Voting Ends</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(contest.votingCloseDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              Pricing Information
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Registration Fee</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  ₦{contest.registrationFee.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Vote Price</span>
                <span className="font-semibold text-gray-900 dark:text-white">
                  ₦{contest.votePrice.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Expected Revenue</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  ₦{(((contest.contestants || 0) * contest.registrationFee) + ((contest.votes || 0) * contest.votePrice)).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-700">
                <span className="text-gray-600 dark:text-gray-400">Status</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(contest.status)}`}>
                  {contest.status.charAt(0).toUpperCase() + contest.status.slice(1)}
                </span>
              </div>
            </div>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href={`/contests/${contest._id}`} target="_blank">
              <Button variant="outline" fullWidth leftIcon={<Eye className="w-4 h-4" />}>
                View Public Page
              </Button>
            </Link>
            <Link href={`/vendor/contests/${contest._id}/contestants`}>
              <Button variant="outline" fullWidth leftIcon={<UserPlus className="w-4 h-4" />}>
                Manage Contestants
              </Button>
            </Link>
            <Button variant="outline" fullWidth leftIcon={<Trophy className="w-4 h-4" />}>
              Declare Winners
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
