"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft, Save, Upload, X } from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Card from "@/components/ui/Card";
import FileUpload from "@/components/ui/FileUpload";
import { ContestsService, Contest, UpdateContestData } from "@/lib/api/contests";
import { handleApiError } from "@/lib/api";

interface ContestFormData {
  title: string;
  description: string;
  registrationFee: number;
  votePrice: number;
  registrationStartDate: string;
  registrationEndDate: string;
  votingStartDate: string;
  votingEndDate: string;
  startsAt: string;
  endsAt: string;
  numberOfWinners: number;
  requireApproval: boolean;
  banner: File | null;
  currentBannerUrl?: string;
}

export default function EditContestPage() {
  const params = useParams();
  const router = useRouter();
  const contestId = params.id as string;

  const [formData, setFormData] = useState<ContestFormData>({
    title: "",
    description: "",
    registrationFee: 0,
    votePrice: 0,
    registrationStartDate: "",
    registrationEndDate: "",
    votingStartDate: "",
    votingEndDate: "",
    startsAt: "",
    endsAt: "",
    numberOfWinners: 1,
    requireApproval: false,
    banner: null
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [errors, setErrors] = useState<Partial<ContestFormData>>({});

  useEffect(() => {
    const fetchContest = async () => {
      try {
        setLoading(true);
        setError(null);
        const contest = await ContestsService.getContest(contestId);

        // Map API data to form data
        setFormData({
          title: contest.title,
          description: contest.description,
          registrationFee: contest.registrationFee,
          votePrice: contest.votePrice,
          registrationStartDate: contest.registrationStartDate.split('T')[0],
          registrationEndDate: contest.registrationCloseDate.split('T')[0],
          votingStartDate: contest.votingStartDate.split('T')[0],
          votingEndDate: contest.votingCloseDate.split('T')[0],
          startsAt: contest.startsAt.split('T')[0],
          endsAt: contest.endsAt.split('T')[0],
          numberOfWinners: contest.numberOfWinners,
          requireApproval: contest.requireApproval,
          banner: null,
          currentBannerUrl: contest.bannerUrl
        });
      } catch (err: any) {
        console.error("Error fetching contest:", err);
        setError(handleApiError(err));
      } finally {
        setLoading(false);
      }
    };

    if (contestId) {
      fetchContest();
    }
  }, [contestId]);

  const handleInputChange = (field: keyof ContestFormData, value: string | number | boolean | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ContestFormData> = {};

    if (!formData.title.trim()) newErrors.title = "Title is required";
    if (!formData.description.trim()) newErrors.description = "Description is required";
    if (formData.registrationFee <= 0) newErrors.registrationFee = "Registration fee must be greater than 0";
    if (formData.votePrice <= 0) newErrors.votePrice = "Vote price must be greater than 0";
    if (!formData.registrationStartDate) newErrors.registrationStartDate = "Registration start date is required";
    if (!formData.registrationEndDate) newErrors.registrationEndDate = "Registration end date is required";
    if (!formData.votingStartDate) newErrors.votingStartDate = "Voting start date is required";
    if (!formData.votingEndDate) newErrors.votingEndDate = "Voting end date is required";
    if (formData.numberOfWinners <= 0) newErrors.numberOfWinners = "Number of winners must be at least 1";

    // Date validations
    if (formData.registrationStartDate && formData.registrationEndDate) {
      if (new Date(formData.registrationStartDate) >= new Date(formData.registrationEndDate)) {
        newErrors.registrationEndDate = "Registration end date must be after start date";
      }
    }

    if (formData.votingStartDate && formData.votingEndDate) {
      if (new Date(formData.votingStartDate) >= new Date(formData.votingEndDate)) {
        newErrors.votingEndDate = "Voting end date must be after start date";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setSaving(true);
    try {
      const updateData: UpdateContestData = {
        title: formData.title,
        description: formData.description,
        registrationFee: formData.registrationFee,
        votePrice: formData.votePrice,
        registrationStartDate: formData.registrationStartDate,
        registrationCloseDate: formData.registrationEndDate,
        votingStartDate: formData.votingStartDate,
        votingCloseDate: formData.votingEndDate,
        startsAt: formData.startsAt,
        endsAt: formData.endsAt,
        numberOfWinners: formData.numberOfWinners,
        requireApproval: formData.requireApproval,
        banner: formData.banner
      };

      await ContestsService.updateContest(contestId, updateData);

      // Redirect back to contest overview
      router.push(`/vendor/contests/${contestId}`);
    } catch (err: any) {
      console.error("Error updating contest:", err);
      setError(handleApiError(err));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Error loading contest</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{error}</p>
          <Link href="/vendor/contests" className="mt-4 inline-block">
            <Button variant="outline">Back to Contests</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/vendor/contests/${contestId}`}>
              <Button variant="ghost" size="sm" leftIcon={<ArrowLeft className="w-4 h-4" />}>
                Back to Contest
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Edit Contest
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Update your contest details and settings
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Basic Information
            </h2>
            
            <div className="space-y-6">
              <Input
                label="Contest Title"
                placeholder="Enter contest title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                error={errors.title}
                required
              />
              
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Description
                </label>
                <textarea
                  placeholder="Describe your contest..."
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description}</p>
                )}
              </div>

              <FileUpload
                label="Contest Banner"
                onFileSelect={(file) => handleInputChange("banner", file)}
                selectedFile={formData.banner}
                accept="image/*"
                maxSize={10}
              />
              
              {formData.currentBannerUrl && !formData.banner && (
                <div className="mt-2">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Current banner:</p>
                  <img 
                    src={formData.currentBannerUrl} 
                    alt="Current banner" 
                    className="w-full h-32 object-cover rounded-lg"
                  />
                </div>
              )}
            </div>
          </Card>

          {/* Pricing */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Pricing
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Registration Fee (₦)"
                type="number"
                placeholder="50000"
                value={formData.registrationFee}
                onChange={(e) => handleInputChange("registrationFee", parseInt(e.target.value) || 0)}
                error={errors.registrationFee}
                required
              />
              
              <Input
                label="Vote Price (₦)"
                type="number"
                placeholder="200"
                value={formData.votePrice}
                onChange={(e) => handleInputChange("votePrice", parseInt(e.target.value) || 0)}
                error={errors.votePrice}
                required
              />
            </div>
          </Card>

          {/* Dates */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Schedule
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Registration Start Date"
                type="date"
                value={formData.registrationStartDate}
                onChange={(e) => handleInputChange("registrationStartDate", e.target.value)}
                error={errors.registrationStartDate}
                required
              />
              
              <Input
                label="Registration End Date"
                type="date"
                value={formData.registrationEndDate}
                onChange={(e) => handleInputChange("registrationEndDate", e.target.value)}
                error={errors.registrationEndDate}
                required
              />
              
              <Input
                label="Voting Start Date"
                type="date"
                value={formData.votingStartDate}
                onChange={(e) => handleInputChange("votingStartDate", e.target.value)}
                error={errors.votingStartDate}
                required
              />
              
              <Input
                label="Voting End Date"
                type="date"
                value={formData.votingEndDate}
                onChange={(e) => handleInputChange("votingEndDate", e.target.value)}
                error={errors.votingEndDate}
                required
              />
            </div>
          </Card>

          {/* Contest Settings */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Contest Settings
            </h2>
            
            <div className="space-y-6">
              <Input
                label="Number of Winners"
                type="number"
                min="1"
                placeholder="1"
                value={formData.numberOfWinners}
                onChange={(e) => handleInputChange("numberOfWinners", parseInt(e.target.value) || 1)}
                error={errors.numberOfWinners}
                required
              />
              
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="requireApproval"
                  checked={formData.requireApproval}
                  onChange={(e) => handleInputChange("requireApproval", e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="requireApproval" className="text-sm font-medium text-gray-900 dark:text-white">
                  Require approval for contestant registration
                </label>
              </div>
            </div>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Link href={`/vendor/contests/${contestId}`}>
              <Button variant="outline">
                Cancel
              </Button>
            </Link>
            <Button 
              type="submit" 
              variant="gradient" 
              isLoading={saving}
              loadingText="Saving..."
              leftIcon={<Save className="w-4 h-4" />}
            >
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
