"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Sun, Moon, Monitor } from "lucide-react";

type Theme = "light" | "dark" | "system";

interface ThemeSwitcherProps {
  className?: string;
  variant?: "button" | "dropdown" | "toggle";
  size?: "sm" | "md" | "lg";
  showLabel?: boolean;
}

const ThemeSwitcher: React.FC<ThemeSwitcherProps> = ({
  className = "",
  variant = "button",
  size = "md",
  showLabel = false,
}) => {
  const [theme, setTheme] = useState<Theme>("system");
  const [mounted, setMounted] = useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  // Ensure component is mounted before rendering to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
    const savedTheme = (localStorage.getItem("theme") as Theme) || "system";
    setTheme(savedTheme);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (newTheme: Theme): void => {
    const root = document.documentElement;
    
    if (newTheme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
      root.classList.toggle("dark", systemTheme === "dark");
    } else {
      root.classList.toggle("dark", newTheme === "dark");
    }
  };

  const changeTheme = (newTheme: Theme): void => {
    setTheme(newTheme);
    localStorage.setItem("theme", newTheme);
    applyTheme(newTheme);
    setIsOpen(false);
  };

  const themes = [
    { value: "light" as Theme, label: "Light", icon: Sun },
    { value: "dark" as Theme, label: "Dark", icon: Moon },
    { value: "system" as Theme, label: "System", icon: Monitor },
  ];

  const currentTheme = themes.find(t => t.value === theme);

  const sizeClasses = {
    sm: "p-1.5 text-sm",
    md: "p-2 text-base",
    lg: "p-3 text-lg",
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
  };

  if (!mounted) {
    return (
      <div className={`${sizeClasses[size]} ${className}`}>
        <div className={`${iconSizes[size]} animate-pulse bg-gray-300 rounded`} />
      </div>
    );
  }

  if (variant === "toggle") {
    const toggleTheme = (): void => {
      const newTheme = theme === "light" ? "dark" : "light";
      changeTheme(newTheme);
    };

    return (
      <motion.button
        onClick={toggleTheme}
        className={`
          relative inline-flex items-center justify-center
          ${sizeClasses[size]}
          bg-gray-100 dark:bg-gray-800
          hover:bg-gray-200 dark:hover:bg-gray-700
          rounded-lg transition-colors duration-200
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
          focus:ring-offset-white dark:focus:ring-offset-black
          ${className}
        `}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label="Toggle theme"
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={theme}
            initial={{ opacity: 0, rotate: -90 }}
            animate={{ opacity: 1, rotate: 0 }}
            exit={{ opacity: 0, rotate: 90 }}
            transition={{ duration: 0.2 }}
          >
            {currentTheme && (
              <currentTheme.icon className={`${iconSizes[size]} text-gray-600 dark:text-gray-400`} />
            )}
          </motion.div>
        </AnimatePresence>
        {showLabel && (
          <span className="ml-2 text-gray-700 dark:text-gray-300">
            {currentTheme?.label}
          </span>
        )}
      </motion.button>
    );
  }

  if (variant === "dropdown") {
    return (
      <div className={`relative ${className}`}>
        <motion.button
          onClick={() => setIsOpen(!isOpen)}
          className={`
            inline-flex items-center justify-center
            ${sizeClasses[size]}
            bg-gray-100 dark:bg-gray-800 
            hover:bg-gray-200 dark:hover:bg-gray-700
            rounded-lg transition-colors duration-200
            focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
          `}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Change theme"
        >
          {currentTheme && (
            <currentTheme.icon className={`${iconSizes[size]} text-gray-600 dark:text-gray-400`} />
          )}
          {showLabel && (
            <span className="ml-2 text-gray-700 dark:text-gray-300">
              {currentTheme?.label}
            </span>
          )}
        </motion.button>

        <AnimatePresence>
          {isOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-10"
                onClick={() => setIsOpen(false)}
              />

              {/* Dropdown */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                transition={{ duration: 0.2 }}
                className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-900 rounded-lg shadow-modern-lg border border-gray-200 dark:border-gray-700 z-20"
              >
                <div className="py-1">
                  {themes.map((themeOption) => (
                    <motion.button
                      key={themeOption.value}
                      onClick={() => changeTheme(themeOption.value)}
                      className={`
                        w-full flex items-center px-4 py-2 text-left
                        hover:bg-gray-100 dark:hover:bg-gray-800
                        transition-colors duration-200
                        ${theme === themeOption.value
                          ? "bg-primary-50 dark:bg-primary-950 text-primary-600 dark:text-primary-400"
                          : "text-gray-700 dark:text-gray-300"
                        }
                      `}
                      whileHover={{ x: 4 }}
                    >
                      <themeOption.icon className={`${iconSizes[size]} mr-3`} />
                      <span>{themeOption.label}</span>
                      {theme === themeOption.value && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="ml-auto w-2 h-2 bg-primary-500 rounded-full"
                        />
                      )}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    );
  }

  // Default button variant
  return (
    <motion.button
      onClick={() => {
        const currentIndex = themes.findIndex(t => t.value === theme);
        const nextIndex = (currentIndex + 1) % themes.length;
        changeTheme(themes[nextIndex].value);
      }}
      className={`
        inline-flex items-center justify-center
        ${sizeClasses[size]}
        bg-gray-100 dark:bg-gray-800 
        hover:bg-gray-200 dark:hover:bg-gray-700
        rounded-lg transition-colors duration-200
        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
        ${className}
      `}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label="Change theme"
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={theme}
          initial={{ opacity: 0, rotate: -90 }}
          animate={{ opacity: 1, rotate: 0 }}
          exit={{ opacity: 0, rotate: 90 }}
          transition={{ duration: 0.2 }}
        >
          {currentTheme && (
            <currentTheme.icon className={`${iconSizes[size]} text-gray-600 dark:text-gray-400`} />
          )}
        </motion.div>
      </AnimatePresence>
      {showLabel && (
        <span className="ml-2 text-gray-700 dark:text-gray-300">
          {currentTheme?.label}
        </span>
      )}
    </motion.button>
  );
};

export default ThemeSwitcher;
