import { apiClient, ApiResponse } from '../api';

// Contest Types
export interface Contest {
  _id: string;
  clientID: string;
  title: string;
  description: string;
  votePrice: number;
  registrationFee: number;
  registrationStartDate: string;
  registrationCloseDate: string;
  votingStartDate: string;
  votingCloseDate: string;
  bannerUrl?: string;
  bannerPublicId?: string;
  logoUrl?: string;
  logoPublicId?: string;
  startsAt: string;
  endsAt: string;
  acceptingRegistrations: boolean;
  acceptingVotes: boolean;
  requireApproval: boolean;
  numberOfWinners: number;
  winners: string[];
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  createdAt: string;
  updatedAt: string;
  slug: string;
  __v: number;
}

export interface CreateContestData {
  title: string;
  description: string;
  votePrice: number;
  registrationFee: number;
  registrationStartDate: string;
  registrationCloseDate: string;
  votingStartDate: string;
  votingCloseDate: string;
  startsAt: string;
  endsAt: string;
  requireApproval: boolean;
  numberOfWinners: number;
  banner?: File;
  logo?: File;
}

export interface UpdateContestData extends Partial<CreateContestData> {
  // All fields are optional for updates
}

export interface ContestResponse {
  success: boolean;
  message: string;
  contest: Contest;
}

export interface ContestsListResponse {
  success: boolean;
  message: string;
  contests: Contest[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Contests API Service
export class ContestsService {
  /**
   * Create a new contest
   */
  static async createContest(data: CreateContestData): Promise<{ success: boolean; message: string; contest: Contest }> {
    const formData = new FormData();

    // Add text fields
    formData.append('title', data.title);
    formData.append('description', data.description);
    formData.append('votePrice', data.votePrice.toString());
    formData.append('registrationFee', data.registrationFee.toString());
    formData.append('registrationStartDate', data.registrationStartDate);
    formData.append('registrationCloseDate', data.registrationCloseDate);
    formData.append('votingStartDate', data.votingStartDate);
    formData.append('votingCloseDate', data.votingCloseDate);
    formData.append('startsAt', data.startsAt);
    formData.append('endsAt', data.endsAt);
    formData.append('requireApproval', data.requireApproval.toString());
    formData.append('numberOfWinners', data.numberOfWinners.toString());

    // Add files
    if (data.banner) {
      formData.append('banner', data.banner);
    }

    const response = await apiClient.post<{ success: boolean; message: string; contest: Contest }>('/contests', formData);
    return response.data!;
  }

  /**
   * Update an existing contest
   */
  static async updateContest(contestId: string, data: UpdateContestData): Promise<Contest> {
    const formData = new FormData();

    // Add text fields if provided
    if (data.title) formData.append('title', data.title);
    if (data.description) formData.append('description', data.description);
    if (data.votePrice !== undefined) formData.append('votePrice', data.votePrice.toString());
    if (data.registrationFee !== undefined) formData.append('registrationFee', data.registrationFee.toString());
    if (data.registrationStartDate) formData.append('registrationStartDate', data.registrationStartDate);
    if (data.registrationCloseDate) formData.append('registrationCloseDate', data.registrationCloseDate);
    if (data.votingStartDate) formData.append('votingStartDate', data.votingStartDate);
    if (data.votingCloseDate) formData.append('votingCloseDate', data.votingCloseDate);
    if (data.startsAt) formData.append('startsAt', data.startsAt);
    if (data.endsAt) formData.append('endsAt', data.endsAt);
    if (data.requireApproval !== undefined) formData.append('requireApproval', data.requireApproval.toString());
    if (data.numberOfWinners !== undefined) formData.append('numberOfWinners', data.numberOfWinners.toString());

    // Add files if provided
    if (data.banner) {
      formData.append('banner', data.banner);
    }
    if (data.logo) {
      formData.append('logo', data.logo);
    }

    const response = await apiClient.put<Contest>(`/contests/${contestId}`, formData);
    return response.data!;
  }

  /**
   * Get contest by ID
   */
  static async getContest(contestId: string): Promise<Contest> {
    const response = await apiClient.get<Contest>(`/contests/${contestId}`);
    return response.data!;
  }

  /**
   * Get contest by slug
   */
  static async getContestBySlug(slug: string): Promise<Contest> {
    const response = await apiClient.get<Contest>(`/contests/slug/${slug}`);
    return response.data!;
  }

  /**
   * Get all contests for the authenticated client
   */
  static async getClientContests(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<Contest[]> {
    const response = await apiClient.get<Contest[]>('/contests/client-contests', params);
    return response.data || [];
  }

  /**
   * Get all public contests (for browsing)
   */
  static async getPublicContests(params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }): Promise<Contest[]> {
    const response = await apiClient.get<Contest[]>('/contests/', params);
    return response.data || [];
  }

  /**
   * Delete a contest
   */
  static async deleteContest(contestId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/contests/${contestId}`);
    return response;
  }

  /**
   * Publish a draft contest
   */
  static async publishContest(contestId: string): Promise<ContestResponse> {
    const response = await apiClient.post<ContestResponse>(`/contests/${contestId}/publish`);
    return response;
  }

  /**
   * Cancel a contest
   */
  static async cancelContest(contestId: string): Promise<ContestResponse> {
    const response = await apiClient.post<ContestResponse>(`/contests/${contestId}/cancel`);
    return response;
  }

  /**
   * Get contest statistics
   */
  static async getContestStats(contestId: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/contests/${contestId}/stats`);
    return response;
  }
}

export default ContestsService;
