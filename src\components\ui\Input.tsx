"use client";

import { forwardRef, InputHTMLAttributes, ReactNode, useState, useId } from "react";
import { motion } from "framer-motion";
import { Eye, EyeOff, AlertCircle, CheckCircle } from "lucide-react";

interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  success?: string;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  variant?: "default" | "outlined" | "filled";
  size?: "sm" | "md" | "lg";
  fullWidth?: boolean;
  showPasswordToggle?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      success,
      helperText,
      leftIcon,
      rightIcon,
      variant = "default",
      size = "md",
      fullWidth = true,
      showPasswordToggle = false,
      type = "text",
      className = "",
      id,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState<boolean>(false);
    const generatedId = useId();

    const inputId = id || generatedId;
    const isPassword = type === "password";
    const inputType = type; // Use the type directly since parent handles password visibility

    const baseClasses = "w-full transition-all duration-200 focus:outline-none placeholder-slate-400 dark:placeholder-slate-500";

    const variantClasses = {
      default: "border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 text-slate-900 dark:text-slate-100",
      outlined: "border-2 border-slate-300 dark:border-slate-600 bg-transparent focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 text-slate-900 dark:text-slate-100",
      filled: "border border-transparent bg-slate-100 dark:bg-slate-700 focus:bg-white dark:focus:bg-slate-800 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 text-slate-900 dark:text-slate-100",
    };

    const sizeClasses = {
      sm: "px-3 py-2 text-sm rounded-lg",
      md: "px-4 py-3 text-base rounded-xl",
      lg: "px-5 py-4 text-lg rounded-xl",
    };

    const iconSizes = {
      sm: "h-4 w-4",
      md: "h-5 w-5",
      lg: "h-6 w-6",
    };

    const getStateClasses = (): string => {
      if (error) {
        return "border-error-500 focus:border-error-500 focus:ring-error-500/20";
      }
      if (success) {
        return "border-success-500 focus:border-success-500 focus:ring-success-500/20";
      }
      return variantClasses[variant];
    };

    const inputClasses = `
      ${baseClasses}
      ${sizeClasses[size]}
      ${getStateClasses()}
      ${leftIcon ? "pl-10" : ""}
      ${rightIcon ? "pr-10" : ""}
      ${fullWidth ? "w-full" : ""}
      ${className}
    `.trim();

    const labelVariants = {
      default: { y: 0, scale: 1 },
      focused: { y: -6, scale: 0.85 },
    };

    return (
      <div className={`${fullWidth ? "w-full" : ""}`}>
        {/* Label */}
        {label && (
          <motion.label
            htmlFor={inputId}
            variants={labelVariants}
            animate={isFocused || props.value ? "focused" : "default"}
            className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2"
          >
            {label}
          </motion.label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
              <span className={iconSizes[size]}>{leftIcon}</span>
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            id={inputId}
            type={inputType}
            className={inputClasses}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...props}
          />

          {/* Right Icon / Password Toggle / Status Icon */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {/* Status Icons */}
            {error && (
              <AlertCircle className={`${iconSizes[size]} text-error-500`} />
            )}
            {success && (
              <CheckCircle className={`${iconSizes[size]} text-success-500`} />
            )}

            {/* Custom Right Icon (includes password toggle if provided) */}
            {rightIcon && (
              <span className={`${iconSizes[size]} text-gray-400`}>
                {rightIcon}
              </span>
            )}
          </div>
        </div>

        {/* Helper Text / Error / Success Message */}
        {(error || success || helperText) && (
          <motion.div
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2"
          >
            {error && (
              <p className="text-sm text-error-500 flex items-center space-x-1">
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </p>
            )}
            {success && (
              <p className="text-sm text-success-500 flex items-center space-x-1">
                <CheckCircle className="h-4 w-4" />
                <span>{success}</span>
              </p>
            )}
            {helperText && !error && !success && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {helperText}
              </p>
            )}
          </motion.div>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
