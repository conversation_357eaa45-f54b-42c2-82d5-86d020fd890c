"use client";

import { motion } from "framer-motion";
import { ArrowRight, Play, Trophy, Users, Star, Sparkles } from "lucide-react";
import Button from "@/components/ui/Button";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";
import { useState, useEffect } from "react";

const HeroSection: React.FC = () => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const changingTexts = [
    "Celebrate Winners.",
    "Power Contests.",
    "Drive Engagement.",
    "Build Communities."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prev) => (prev + 1) % changingTexts.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [changingTexts.length]);

  const stats = [
    { icon: Trophy, value: "10K+", label: "Contests Created", color: "text-secondary-500" },
    { icon: Users, value: "50K+", label: "Active Users", color: "text-primary-600" },
    { icon: Star, value: "4.9", label: "User Rating", color: "text-primary-500" },
  ];

  const floatingElements = [
    { icon: Trophy, delay: 0, x: "10%", y: "20%", color: "text-secondary-500" },
    { icon: Star, delay: 0.5, x: "80%", y: "30%", color: "text-primary-500" },
    { icon: Sparkles, delay: 1, x: "15%", y: "70%", color: "text-primary-600" },
    { icon: Users, delay: 1.5, x: "85%", y: "80%", color: "text-accent-500" },
  ];

  return (
    <section
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-black dark:via-gray-900 dark:to-black"
      data-aos="fade-in"
      data-aos-duration="1000"
    >
      {/* Modern Professional Background Effects */}
      <div className="absolute inset-0">
        {/* Animated Grid Pattern */}
        <motion.div
          className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(59,130,246,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.08)_1px,transparent_1px)] bg-[size:50px_50px]"
          animate={{
            backgroundPosition: ["0px 0px", "50px 50px"],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Dynamic Gradient Orbs */}
        <motion.div
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-primary-500/20 to-secondary-500/15 dark:from-primary-500/10 dark:to-secondary-500/8 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-secondary-500/20 to-primary-600/15 dark:from-secondary-500/8 dark:to-primary-600/6 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.6, 0.3, 0.6],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
        />

        {/* Central Spotlight Effect */}
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-primary-500/12 via-transparent to-transparent dark:from-primary-500/6 rounded-full"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.5, 0.8, 0.5],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Animated Beams */}
        <motion.div
          className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-primary-500/40 dark:via-primary-500/20 to-transparent"
          animate={{
            opacity: [0.2, 0.8, 0.2],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-secondary-500/40 dark:via-secondary-500/20 to-transparent"
          animate={{
            opacity: [0.8, 0.2, 0.8],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-primary-400/30 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 2) * 40}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5,
            }}
          />
        ))}
      </div>

      {/* Floating Elements */}
      {floatingElements.map((element, index) => (
        <motion.div
          key={index}
          className="absolute hidden lg:block"
          style={{ left: element.x, top: element.y }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: 0.6,
            scale: 1,
            y: [0, -30, 0],
            rotateZ: [0, 360],
          }}
          transition={{
            delay: element.delay,
            duration: 2,
            y: {
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
            },
            rotateZ: {
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            },
          }}
        >
          <element.icon className={`h-10 w-10 ${element.color} drop-shadow-lg`} />
        </motion.div>
      ))}

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          animate="visible"
          className="text-center max-w-5xl mx-auto"
        >
          {/* Badge */}
          <motion.div
            variants={staggerItemVariants}
            className="inline-flex items-center space-x-2 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-4 py-2 rounded-full text-sm font-medium mb-8"
          >
            <Sparkles className="h-4 w-4" />
            <span>Trusted by 50,000+ users worldwide</span>
          </motion.div>

          {/* Main Heading with Changing Text */}
          <motion.h1
            variants={staggerItemVariants}
            className="text-5xl sm:text-6xl lg:text-7xl font-bold font-display leading-tight mb-8"
          >
            <motion.span
              className="bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-500 bg-clip-text text-transparent bg-[length:200%_auto] animate-gradient-shift block"
              key={currentTextIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              {changingTexts[currentTextIndex]}
            </motion.span>
            <span className="text-primary-950 dark:text-white block mt-2">
              Transform Your Vision.
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            variants={staggerItemVariants}
            className="text-xl lg:text-2xl text-gray-700 dark:text-gray-200 mb-12 max-w-4xl mx-auto leading-relaxed"
          >
            The next-generation platform for creating, managing, and conducting secure voting
            for contests and awards. Powered by cutting-edge technology and AI-driven insights.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={staggerItemVariants}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-16"
          >
            <Button
              variant="gradient"
              size="xl"
              rightIcon={<ArrowRight className="h-6 w-6" />}
              glow={true}
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 },
              }}
              className="px-10 py-5 text-xl font-semibold"
            >
              Create Contest
            </Button>

            <Button
              variant="outline"
              size="xl"
              leftIcon={<Play className="h-6 w-6" />}
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 },
              }}
              className="px-10 py-5 text-xl font-semibold"
            >
              Watch Demo
            </Button>
          </motion.div>

          {/* Stats */}
          <motion.div
            variants={staggerItemVariants}
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={staggerItemVariants}
                className="text-center group"
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="bg-white/70 dark:bg-black/60 backdrop-blur-sm border border-orange-200/50 dark:border-orange-500/20 rounded-2xl p-6 group-hover:border-primary-500/50 transition-all duration-300 group-hover:shadow-orange-glow">
                  <div className="flex items-center justify-center mb-4">
                    <stat.icon className={`h-10 w-10 ${stat.color} mr-3 group-hover:scale-110 transition-transform duration-300`} />
                    <span className="text-4xl lg:text-5xl font-bold font-display text-gray-900 dark:text-white">
                      {stat.value}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 font-medium text-lg">
                    {stat.label}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 0.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="w-6 h-10 border-2 border-gray-400 dark:border-gray-600 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="w-1 h-3 bg-gray-400 dark:bg-gray-600 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default HeroSection;
