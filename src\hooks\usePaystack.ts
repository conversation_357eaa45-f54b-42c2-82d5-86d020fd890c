"use client";

import { useState, useCallback } from 'react';
import { 
  PaymentData, 
  generatePaymentReference, 
  initializePayment, 
  verifyPayment,
  format<PERSON>air<PERSON>,
  nairaToKobo
} from '@/lib/paystack';

interface UsePaystackOptions {
  onSuccess?: (reference: string, transaction: any) => void;
  onError?: (error: string) => void;
  onClose?: () => void;
}

interface PaystackPopupOptions {
  key: string;
  email: string;
  amount: number;
  currency: string;
  ref: string;
  callback: (response: any) => void;
  onClose: () => void;
  metadata?: any;
}

// Declare Paystack popup function
declare global {
  interface Window {
    PaystackPop: {
      setup: (options: PaystackPopupOptions) => {
        openIframe: () => void;
      };
    };
  }
}

export function usePaystack(options: UsePaystackOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);

  // Load Paystack script
  const loadPaystackScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if (window.PaystackPop) {
        setIsScriptLoaded(true);
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.async = true;
      
      script.onload = () => {
        setIsScriptLoaded(true);
        resolve();
      };
      
      script.onerror = () => {
        reject(new Error('Failed to load Paystack script'));
      };

      document.head.appendChild(script);
    });
  }, []);

  // Initialize payment
  const initializeContestPayment = useCallback(async (
    contestId: string,
    contestantEmail: string,
    amount: number,
    contestTitle: string,
    contestantName?: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      // Load Paystack script if not already loaded
      if (!isScriptLoaded) {
        await loadPaystackScript();
      }

      const reference = generatePaymentReference('ENTRY');
      
      const paymentData: PaymentData = {
        email: contestantEmail,
        amount: amount,
        currency: 'NGN',
        reference,
        metadata: {
          contest_id: contestId,
          payment_type: 'entry_fee',
          contest_title: contestTitle,
          contestant_name: contestantName,
          contestant_email: contestantEmail
        }
      };

      // Initialize payment with backend
      const initResponse = await initializePayment(paymentData);
      
      if (!initResponse.status) {
        throw new Error(initResponse.message || 'Payment initialization failed');
      }

      // Open Paystack popup
      const popup = window.PaystackPop.setup({
        key: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY!,
        email: contestantEmail,
        amount: nairaToKobo(amount),
        currency: 'NGN',
        ref: reference,
        metadata: paymentData.metadata,
        callback: async (response: any) => {
          try {
            // Verify payment on backend
            const verification = await verifyPayment(response.reference);
            
            if (verification.status && verification.data.status === 'success') {
              options.onSuccess?.(response.reference, verification.data);
            } else {
              throw new Error('Payment verification failed');
            }
          } catch (verifyError) {
            const errorMessage = verifyError instanceof Error ? verifyError.message : 'Payment verification failed';
            setError(errorMessage);
            options.onError?.(errorMessage);
          } finally {
            setIsLoading(false);
          }
        },
        onClose: () => {
          setIsLoading(false);
          options.onClose?.();
        }
      });

      popup.openIframe();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment initialization failed';
      setError(errorMessage);
      options.onError?.(errorMessage);
      setIsLoading(false);
    }
  }, [isScriptLoaded, loadPaystackScript, options]);

  // Verify payment status
  const verifyPaymentStatus = useCallback(async (reference: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const verification = await verifyPayment(reference);
      setIsLoading(false);
      return verification;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment verification failed';
      setError(errorMessage);
      setIsLoading(false);
      throw error;
    }
  }, []);

  return {
    initializeContestPayment,
    verifyPaymentStatus,
    isLoading,
    error,
    isScriptLoaded,
    formatNaira
  };
}

// Hook for prize distribution payments
export function usePrizeDistribution() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const distributePrize = useCallback(async (
    winnerEmail: string,
    winnerName: string,
    amount: number,
    contestTitle: string,
    bankDetails: {
      accountNumber: string;
      bankCode: string;
      accountName: string;
    }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      // This would typically involve:
      // 1. Creating a transfer recipient
      // 2. Initiating the transfer
      // 3. Sending notification to winner
      
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, you would call the Paystack transfer APIs
      console.log('Prize distribution initiated:', {
        winnerEmail,
        winnerName,
        amount: formatNaira(amount),
        contestTitle,
        bankDetails
      });

      setIsLoading(false);
      return {
        success: true,
        message: `Prize of ${formatNaira(amount)} has been sent to ${winnerName}`,
        reference: generatePaymentReference('PRIZE')
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Prize distribution failed';
      setError(errorMessage);
      setIsLoading(false);
      throw error;
    }
  }, []);

  return {
    distributePrize,
    isLoading,
    error,
    formatNaira
  };
}

export default usePaystack;
