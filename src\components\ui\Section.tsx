"use client";

import { forwardRef, HTMLAttributes, ReactNode } from "react";

interface SectionProps extends HTMLAttributes<HTMLElement> {
  children: ReactNode;
  variant?: "default" | "primary" | "secondary" | "dark" | "light";
  size?: "sm" | "md" | "lg" | "xl" | "2xl";
  fullWidth?: boolean;
  centered?: boolean;
  as?: "section" | "div" | "article" | "aside";
}

const Section = forwardRef<HTMLElement, SectionProps>(
  (
    {
      children,
      variant = "default",
      size = "lg",
      fullWidth = false,
      centered = false,
      as: Component = "section",
      className = "",
      ...props
    },
    ref
  ) => {
    const baseClasses = "w-full";

    const variantClasses = {
      default: "bg-background-light dark:bg-background-dark",
      primary: "bg-primary-50 dark:bg-primary-900/20",
      secondary: "bg-secondary-50 dark:bg-secondary-900/20",
      dark: "bg-gray-900 text-white",
      light: "bg-slate-50 dark:bg-slate-800",
    };

    const sizeClasses = {
      sm: "py-8 lg:py-12",
      md: "py-12 lg:py-16",
      lg: "py-16 lg:py-20",
      xl: "py-20 lg:py-24",
      "2xl": "py-24 lg:py-32",
    };

    const containerClasses = fullWidth
      ? "w-full"
      : "container mx-auto px-4 sm:px-6 lg:px-8";

    const contentClasses = centered ? "text-center" : "";

    const sectionClasses = `
      ${baseClasses}
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${className}
    `.trim();

    return (
      <Component ref={ref as any} className={sectionClasses} {...props}>
        <div className={containerClasses}>
          <div className={contentClasses}>{children}</div>
        </div>
      </Component>
    );
  }
);

Section.displayName = "Section";

// Section Header Component
interface SectionHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  centered?: boolean;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "full";
}

export const SectionHeader = forwardRef<HTMLDivElement, SectionHeaderProps>(
  (
    { 
      children, 
      centered = true, 
      maxWidth = "3xl", 
      className = "", 
      ...props 
    }, 
    ref
  ) => {
    const maxWidthClasses = {
      sm: "max-w-sm",
      md: "max-w-md",
      lg: "max-w-lg",
      xl: "max-w-xl",
      "2xl": "max-w-2xl",
      "3xl": "max-w-3xl",
      "4xl": "max-w-4xl",
      full: "max-w-full",
    };

    const headerClasses = `
      ${maxWidthClasses[maxWidth]}
      ${centered ? "mx-auto text-center" : ""}
      mb-12 lg:mb-16
      ${className}
    `.trim();

    return (
      <div ref={ref} className={headerClasses} {...props}>
        {children}
      </div>
    );
  }
);

SectionHeader.displayName = "SectionHeader";

// Section Title Component
interface SectionTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  size?: "sm" | "md" | "lg" | "xl" | "2xl";
  gradient?: boolean;
}

export const SectionTitle = forwardRef<HTMLHeadingElement, SectionTitleProps>(
  (
    { 
      children, 
      as: Component = "h2", 
      size = "lg", 
      gradient = false,
      className = "", 
      ...props 
    }, 
    ref
  ) => {
    const sizeClasses = {
      sm: "text-2xl lg:text-3xl",
      md: "text-3xl lg:text-4xl",
      lg: "text-4xl lg:text-5xl",
      xl: "text-5xl lg:text-6xl",
      "2xl": "text-6xl lg:text-7xl",
    };

    const gradientClasses = gradient
      ? "bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent"
      : "text-gray-900 dark:text-white";

    const titleClasses = `
      ${sizeClasses[size]}
      ${gradientClasses}
      font-bold leading-tight tracking-tight
      mb-4
      ${className}
    `.trim();

    return (
      <Component ref={ref} className={titleClasses} {...props}>
        {children}
      </Component>
    );
  }
);

SectionTitle.displayName = "SectionTitle";

// Section Subtitle Component
interface SectionSubtitleProps extends HTMLAttributes<HTMLParagraphElement> {
  children: ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
}

export const SectionSubtitle = forwardRef<HTMLParagraphElement, SectionSubtitleProps>(
  ({ children, size = "lg", className = "", ...props }, ref) => {
    const sizeClasses = {
      sm: "text-base lg:text-lg",
      md: "text-lg lg:text-xl",
      lg: "text-xl lg:text-2xl",
      xl: "text-2xl lg:text-3xl",
    };

    const subtitleClasses = `
      ${sizeClasses[size]}
      text-gray-600 dark:text-gray-400
      leading-relaxed
      ${className}
    `.trim();

    return (
      <p ref={ref} className={subtitleClasses} {...props}>
        {children}
      </p>
    );
  }
);

SectionSubtitle.displayName = "SectionSubtitle";

export default Section;
