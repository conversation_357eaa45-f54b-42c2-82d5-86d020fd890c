# Contestr - Modern Contest Management Platform

A sleek, modern contest management platform built with Next.js 14, TypeScript, and Tailwind CSS. Contestr provides a comprehensive solution for creating, managing, and running contests with beautiful UI/UX and powerful features.

## ✨ Features

### 🏆 Contest Management
- **Create Contests** - Set up contests with custom categories, rules, and voting criteria
- **Collect Nominations** - Easy submission process with powerful moderation tools
- **Secure Voting** - Multiple authentication methods with real-time analytics
- **Celebrate Winners** - Beautiful winner pages with digital certificates

### 🎨 Modern Design
- **Dark/Light Mode** - Seamless theme switching with system preference detection
- **Responsive Design** - Mobile-first approach with perfect tablet and desktop layouts
- **3D Effects & Animations** - Framer Motion animations with AOS scroll effects
- **Modern Aesthetic** - Vibrant orange and red gradient color scheme

### 🛠️ Technical Features
- **Next.js 14** - Latest App Router with TypeScript support
- **Tailwind CSS** - Utility-first CSS framework with custom design system
- **Framer Motion** - Smooth animations and micro-interactions
- **Component Library** - Reusable UI components with consistent styling
- **SEO Optimized** - Meta tags, structured data, and performance optimization

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd contestr
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (pages)/           # Page routes
│   ├── globals.css        # Global styles and CSS variables
│   └── layout.tsx         # Root layout component
├── components/            # React components
│   ├── layout/           # Layout components (Header, Footer)
│   ├── sections/         # Page sections (Hero, Features, etc.)
│   ├── shared/           # Shared components (ThemeSwitcher, etc.)
│   └── ui/               # UI components (Button, Card, Input, etc.)
├── utils/                # Utility functions
│   └── framerVariants.ts # Animation variants
└── types/                # TypeScript type definitions
```

## 🎨 Design System

### Color Palette
- **Primary**: Orange gradient (#F97316 to #C2410C)
- **Secondary**: Red gradient (#EF4444 to #B91C1C)
- **Accent**: Yellow-orange (#F59E0B)
- **Success**: Green (#10B981)
- **Error**: Red (#EF4444)

### Typography
- **Font Family**: Sinkin Sans (preferred), Poppins, Montserrat
- **Font Weights**: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)

### Components
- **Cards**: Elevated design with subtle shadows and hover effects
- **Buttons**: Multiple variants (primary, secondary, outline, ghost)
- **Inputs**: Floating labels with focus states and validation
- **Animations**: Smooth transitions with spring physics

## 🌓 Theme System

The application supports both light and dark modes with:
- System preference detection
- Manual theme switching
- Persistent theme storage
- Smooth transitions between themes
- Proper contrast ratios for accessibility

## 📱 Pages

- **Home** (`/`) - Hero section, features, how it works, testimonials
- **About** (`/about`) - Company information, team, mission
- **Services** (`/services`) - Service offerings with detailed descriptions
- **Contact** (`/contact`) - Contact form, company information, FAQ

## 🔧 Recent Updates

### UI/UX Improvements
- ✅ Fixed hydration errors with stable ID generation
- ✅ Resolved text visibility issues in both light and dark modes
- ✅ Fixed step numbering visibility in "How It Works" section
- ✅ Corrected hamburger menu colors in mobile dark mode
- ✅ Enhanced quote icon positioning in testimonials
- ✅ Improved form input styling and accessibility

### Technical Enhancements
- ✅ Implemented React's `useId()` for consistent server/client rendering
- ✅ Fixed AnimatePresence warnings for better performance
- ✅ Updated color system with proper slate color palette
- ✅ Enhanced z-index management for proper element layering
- ✅ Improved overflow handling for positioned elements

## 🛠️ Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Style
- TypeScript for type safety
- ESLint for code quality
- Consistent component structure
- Proper prop typing
- Responsive design patterns

## 📦 Dependencies

### Core
- **Next.js 14** - React framework
- **React 18** - UI library
- **TypeScript** - Type safety

### Styling
- **Tailwind CSS** - Utility-first CSS
- **Framer Motion** - Animation library
- **AOS** - Scroll animations

### Icons & UI
- **Lucide React** - Icon library
- **Custom Components** - Reusable UI elements

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Configure build settings (auto-detected)
3. Deploy with automatic CI/CD

### Other Platforms
- **Netlify**: Configure build command as `npm run build`
- **AWS Amplify**: Use Next.js preset
- **Docker**: Use the included Dockerfile (if available)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Tailwind CSS for the utility-first approach
- Framer Motion for smooth animations
- Lucide for beautiful icons
- Vercel for hosting and deployment
