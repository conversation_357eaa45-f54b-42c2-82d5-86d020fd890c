"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  Settings,
  Users,
  TrendingUp,
  Calendar,
  DollarSign,
  Trophy,
  Clock
} from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Card from "@/components/ui/Card";
import { ContestsService, Contest } from "@/lib/api/contests";
import { handleApiError } from "@/lib/api";

// Contest interface is now imported from API

export default function VendorContestsPage() {
  const [contests, setContests] = useState<Contest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Fetch contests from API
  useEffect(() => {
    const fetchContests = async () => {
      try {
        setLoading(true);
        setError(null);
        const contestsData = await ContestsService.getClientContests();
        setContests(contestsData);
      } catch (err: any) {
        console.error("Error fetching contests:", err);
        setError(handleApiError(err));
      } finally {
        setLoading(false);
      }
    };

    fetchContests();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "draft": return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      case "completed": return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "paused": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const filteredContests = contests.filter(contest => {
    const matchesSearch = contest.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         contest.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || contest.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Calculate stats from actual contest data
  const totalContestants = contests.reduce((sum, contest) => sum + (contest.contestants || 0), 0);
  const totalRevenue = contests.reduce((sum, contest) => sum + (contest.revenue || 0), 0);
  const activeContests = contests.filter(c => c.status === "active").length;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              My Contests
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage and monitor your contests
            </p>
          </div>
          <Link href="/vendor/create-contest">
            <Button variant="gradient" leftIcon={<Plus className="w-4 h-4" />}>
              Create Contest
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search contests..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="paused">Paused</option>
            </select>
          </div>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="p-6 border-red-200 dark:border-red-800">
            <div className="text-red-600 dark:text-red-400">
              <p className="font-medium">Error loading contests</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </Card>
        )}

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Contests</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{contests.length}</p>
              </div>
              <Trophy className="w-8 h-8 text-primary-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Contests</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activeContests}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Contestants</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {totalContestants}
                </p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ₦{totalRevenue.toLocaleString()}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-orange-500" />
            </div>
          </Card>
        </div>

        {/* Contests Grid */}
        {filteredContests.length === 0 ? (
          <Card className="p-12 text-center">
            <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No contests found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {searchQuery || statusFilter !== "all" 
                ? "Try adjusting your search or filters"
                : "Create your first contest to get started"
              }
            </p>
            {!searchQuery && statusFilter === "all" && (
              <Link href="/vendor/create-contest">
                <Button variant="gradient" leftIcon={<Plus className="w-4 h-4" />}>
                  Create Your First Contest
                </Button>
              </Link>
            )}
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredContests.map((contest) => (
              <motion.div
                key={contest.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="overflow-hidden hover:shadow-lg transition-shadow">
                  {/* Contest Image */}
                  {contest.bannerUrl && (
                    <div className="h-48 bg-gradient-to-r from-primary-500 to-secondary-500 relative">
                      <img
                        src={contest.bannerUrl}
                        alt={contest.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-4 right-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(contest.status)}`}>
                          {contest.status.charAt(0).toUpperCase() + contest.status.slice(1)}
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {contest.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                      {contest.description}
                    </p>

                    {/* Stats */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center">
                        <p className="text-lg font-bold text-gray-900 dark:text-white">{contest.contestants || 0}</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Contestants</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold text-gray-900 dark:text-white">{contest.votes || 0}</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Votes</p>
                      </div>
                    </div>

                    {/* Revenue */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Registration Fee</span>
                      <span className="font-semibold text-green-600 dark:text-green-400">
                        ₦{contest.registrationFee.toLocaleString()}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Link href={`/vendor/contests/${contest._id}`} className="flex-1">
                        <Button variant="outline" size="sm" fullWidth leftIcon={<Eye className="w-4 h-4" />}>
                          View
                        </Button>
                      </Link>
                      <Link href={`/vendor/contests/${contest._id}/edit`}>
                        <Button variant="ghost" size="sm" leftIcon={<Edit className="w-4 h-4" />}>
                          Edit
                        </Button>
                      </Link>
                      <Link href={`/vendor/contests/${contest._id}/settings`}>
                        <Button variant="ghost" size="sm" leftIcon={<Settings className="w-4 h-4" />}>
                          Settings
                        </Button>
                      </Link>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
