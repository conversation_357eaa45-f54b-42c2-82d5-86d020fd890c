"use client";

import { motion } from "framer-motion";
import { PlusCircle, Users, Vote, Trophy, ArrowRight } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const HowItWorksSection: React.FC = () => {
  const steps = [
    {
      icon: PlusCircle,
      title: "Create Contest",
      description: "Set up your contest with custom categories, rules, and voting criteria. Our intuitive builder makes it easy.",
      features: ["Custom categories", "Flexible rules", "Branding options"],
      color: "from-primary-600 to-primary-500",
      glowColor: "shadow-orange-glow",
    },
    {
      icon: Users,
      title: "Collect Nominations",
      description: "Invite participants to submit their entries. Manage submissions with our powerful moderation tools.",
      features: ["Easy submissions", "Moderation tools", "Bulk management"],
      color: "from-accent-500 to-accent-600",
      glowColor: "shadow-red-glow",
    },
    {
      icon: Vote,
      title: "Secure Voting",
      description: "Launch voting with multiple authentication methods. Real-time analytics keep you informed.",
      features: ["Multiple auth methods", "Real-time analytics", "Fraud prevention"],
      color: "from-primary-500 to-accent-500",
      glowColor: "shadow-orange-glow",
    },
    {
      icon: Trophy,
      title: "Celebrate Winners",
      description: "Announce results with beautiful winner pages. Generate certificates and share achievements.",
      features: ["Winner announcements", "Digital certificates", "Social sharing"],
      color: "from-accent-600 to-primary-600",
      glowColor: "shadow-red-glow",
    },
  ];

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="100"
    >
      {/* Modern Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-80 h-80 bg-gradient-to-r from-primary-500/10 to-accent-500/5 dark:from-primary-500/20 dark:to-accent-500/10 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-accent-500/10 to-primary-500/5 dark:from-accent-500/20 dark:to-primary-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: "1s" }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-primary-500/5 to-accent-500/5 dark:from-primary-500/10 dark:to-accent-500/10 rounded-full blur-3xl animate-pulse-slow" />
      </div>

      <div className="relative z-10">
        <SectionHeader data-aos="fade-up" data-aos-delay="200">
          <SectionTitle gradient>How It Works</SectionTitle>
          <SectionSubtitle>
            From concept to celebration in four simple steps.
            Our platform handles the complexity so you can focus on what matters.
          </SectionSubtitle>
        </SectionHeader>

        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-4 gap-8 relative pt-6"
        >
          {steps.map((step, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
              className="relative"
            >
              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-20 left-full w-full h-1 bg-gradient-to-r from-border-primary/30 to-transparent z-0 rounded-full">
                  <motion.div
                    className={`h-full bg-gradient-to-r ${step.color} rounded-full shadow-glow-sm`}
                    initial={{ width: 0 }}
                    whileInView={{ width: "100%" }}
                    transition={{ delay: index * 0.3 + 0.5, duration: 1.2 }}
                    viewport={{ once: true }}
                  />
                </div>
              )}

              <Card
                variant="elevated"
                hover
                interactive
                glow={index % 2 === 0 ? "orange" : "red"}
                className="relative z-10 h-full group overflow-visible"
                motionProps={{
                  whileHover: { y: -8, scale: 1.02 },
                  transition: { type: "spring", stiffness: 300 }
                }}
              >
                {/* Step Number */}
                <div className={`absolute -top-4 -left-4 w-10 h-10 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-all duration-300 z-20`}>
                  {index + 1}
                </div>

                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-r ${step.color} mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 ${step.glowColor}`}>
                  <step.icon className="h-10 w-10 text-white drop-shadow-lg" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold font-display text-slate-900 dark:text-slate-100 mb-4">
                  {step.title}
                </h3>

                <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed text-lg">
                  {step.description}
                </p>

                {/* Features */}
                <ul className="space-y-2">
                  {step.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center text-sm text-slate-600 dark:text-slate-400"
                    >
                      <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* Hover Arrow */}
                <motion.div
                  className="absolute bottom-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ x: -10 }}
                  whileHover={{ x: 0 }}
                >
                  <ArrowRight className="h-5 w-5 text-primary-500" />
                </motion.div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 px-6 py-3 rounded-full">
            <span className="text-gray-700 dark:text-gray-300">
              Ready to get started?
            </span>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-primary-600 dark:text-primary-400 font-medium hover:underline"
            >
              Create your first contest →
            </motion.button>
          </div>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default HowItWorksSection;
