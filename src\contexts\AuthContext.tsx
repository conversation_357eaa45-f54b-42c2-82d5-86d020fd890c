"use client";

import { createContext, useContext, useReducer, useEffect, ReactNode } from "react";
import { AuthService } from "@/lib/api/auth";
import { handleApiError } from "@/lib/api";

// Types
export type UserRole = "admin" | "vendor" | "voter";

export interface User {
  id: string;
  email?: string;
  name?: string;
  role: UserRole;
  isEmailVerified?: boolean;
  isApproved?: boolean; // For vendors
  organizationName?: string;
  avatar?: string;
  bio?: string;
  createdAt?: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean; // For initialization
  isSubmitting: boolean; // For form submissions
  isAuthenticated: boolean;
  error: string | null;
}

// Actions
type AuthAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_SUBMITTING"; payload: boolean }
  | { type: "SET_USER"; payload: User | null }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "LOGOUT" }
  | { type: "UPDATE_USER"; payload: Partial<User> };

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: true,
  isSubmitting: false,
  isAuthenticated: false,
  error: null,
};

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };
    case "SET_SUBMITTING":
      return {
        ...state,
        isSubmitting: action.payload,
      };
    case "SET_USER":
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        isLoading: false,
        isSubmitting: false,
        error: null,
      };
    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
        isLoading: false,
        isSubmitting: false,
      };
    case "LOGOUT":
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        isSubmitting: false,
        error: null,
      };
    case "UPDATE_USER":
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    default:
      return state;
  }
}

// Context
interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  resendVerification: (email: string) => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  refreshUser: () => Promise<void>;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  bio: string;
  file?: File;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      dispatch({ type: "SET_LOADING", payload: true });

      // Check if user is authenticated
      if (!AuthService.isAuthenticated()) {
        dispatch({ type: "SET_LOADING", payload: false });
        return;
      }

      // Get user info from token
      const userInfo = AuthService.getCurrentUser();
      if (userInfo) {
        const user: User = {
          id: userInfo.id,
          email: userInfo.email,
          role: "vendor", // Default to vendor for now
          isEmailVerified: true,
          isApproved: true,
        };

        dispatch({ type: "SET_USER", payload: user });
      }
    } catch (error) {
      console.error("Auth initialization error:", error);
      localStorage.removeItem("token");
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      dispatch({ type: "SET_SUBMITTING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      const response = await AuthService.login({ email, password });

      if (response.success && response.token) {
        // Get user info from the stored token
        const userInfo = AuthService.getCurrentUser();
        if (userInfo) {
          const user: User = {
            id: userInfo.id,
            email: email,
            name: email.split('@')[0], // Use email prefix as name
            role: "vendor", // Based on API documentation, all registered users are vendors
            isEmailVerified: true, // Assume verified if login successful
            isApproved: true,
          };

          dispatch({ type: "SET_USER", payload: user });
        }
      } else {
        throw new Error(response.message || "Login failed");
      }

    } catch (error: any) {
      console.error("Login error:", error);
      const errorMessage = handleApiError(error);
      dispatch({ type: "SET_ERROR", payload: errorMessage });
      throw error;
    } finally {
      console.log("Login: Setting submitting to false");
      dispatch({ type: "SET_SUBMITTING", payload: false });
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      dispatch({ type: "SET_SUBMITTING", payload: true });
      dispatch({ type: "SET_ERROR", payload: null });

      const registerData = {
        name: userData.name,
        email: userData.email,
        password: userData.password,
        bio: userData.bio || 'Vendor account',
        file: userData.file, // Include the file if provided
      };

      const response = await AuthService.register(registerData);

      if (response.success) {
        // Registration successful, user needs to verify email
        dispatch({ type: "SET_SUBMITTING", payload: false });
      } else {
        throw new Error(response.message || "Registration failed");
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      const errorMessage = handleApiError(error);
      dispatch({ type: "SET_ERROR", payload: errorMessage });
      throw error;
    } finally {
      dispatch({ type: "SET_SUBMITTING", payload: false });
    }
  };

  const logout = async () => {
    try {
      await AuthService.logout();
      dispatch({ type: "LOGOUT" });
    } catch (error) {
      console.error("Logout error:", error);
      // Even if logout fails, clear local state
      dispatch({ type: "LOGOUT" });
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      // TODO: Replace with actual API call
      console.log("Forgot password request:", email);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
    } catch (error) {
      console.error("Forgot password error:", error);
      throw error;
    }
  };

  const resetPassword = async (token: string, password: string) => {
    try {
      // TODO: Replace with actual API call
      console.log("Reset password:", { token, password });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error("Reset password error:", error);
      throw error;
    }
  };

  const verifyEmail = async (token: string) => {
    try {
      // TODO: Replace with actual API call
      console.log("Verify email:", token);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error("Email verification error:", error);
      throw error;
    }
  };

  const resendVerification = async (email: string) => {
    try {
      // TODO: Replace with actual API call
      console.log("Resend verification:", email);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
    } catch (error) {
      console.error("Resend verification error:", error);
      throw error;
    }
  };

  const updateProfile = async (userData: Partial<User>) => {
    try {
      dispatch({ type: "SET_SUBMITTING", payload: true });
      
      // TODO: Replace with actual API call
      console.log("Update profile:", userData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      dispatch({ type: "UPDATE_USER", payload: userData });
    } catch (error) {
      console.error("Update profile error:", error);
      dispatch({ type: "SET_ERROR", payload: "Failed to update profile" });
      throw error;
    } finally {
      dispatch({ type: "SET_SUBMITTING", payload: false });
    }
  };

  const refreshUser = async () => {
    try {
      // TODO: Replace with actual API call to get fresh user data
      console.log("Refreshing user data");
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error("Refresh user error:", error);
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
