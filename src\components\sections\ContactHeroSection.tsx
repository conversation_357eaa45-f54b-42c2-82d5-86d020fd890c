"use client";

import { motion } from "framer-motion";
import { Mail, Phone, MapPin, Clock, MessageCircle, Headphones } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import Card from "@/components/ui/Card";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const ContactHeroSection: React.FC = () => {
  const contactMethods = [
    {
      icon: Mail,
      title: "Email Support",
      description: "Get help via email",
      contact: "<EMAIL>",
      availability: "24/7 response",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak with our team",
      contact: "+****************",
      availability: "Mon-Fri, 9AM-6PM PST",
      color: "from-green-500 to-emerald-500",
    },
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Instant messaging support",
      contact: "Available on website",
      availability: "24/7 online",
      color: "from-purple-500 to-pink-500",
    },
  ];

  const officeInfo = [
    {
      icon: MapPin,
      title: "Headquarters",
      details: ["123 Innovation Drive", "San Francisco, CA 94105", "United States"],
    },
    {
      icon: Clock,
      title: "Business Hours",
      details: ["Monday - Friday: 9:00 AM - 6:00 PM PST", "Saturday: 10:00 AM - 4:00 PM PST", "Sunday: Closed"],
    },
    {
      icon: Headphones,
      title: "Support Hours",
      details: ["24/7 Email Support", "Live Chat: 24/7", "Phone: Mon-Fri 9AM-6PM PST"],
    },
  ];

  return (
    <AnimatedSection
      variant="primary"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="100"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-secondary-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-primary-400 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="text-center max-w-4xl mx-auto mb-16"
        >
          <motion.h1
            variants={staggerItemVariants}
            className="text-5xl sm:text-6xl lg:text-7xl font-bold leading-tight mb-6"
          >
            <span className="text-primary-950 dark:text-white">
              Get in
            </span>
            <br />
            <span className="bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent">
              Touch
            </span>
          </motion.h1>

          <motion.p
            variants={staggerItemVariants}
            className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed"
          >
            Have questions about Kontestica? Need help with your contest? 
            Our friendly team is here to help you succeed.
          </motion.p>

          <motion.div
            variants={staggerItemVariants}
            className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm text-slate-700 dark:text-white px-6 py-3 rounded-full"
          >
            <Headphones className="h-5 w-5" />
            <span className="font-medium">Average response time: Under 2 hours</span>
          </motion.div>
        </motion.div>

        {/* Contact Methods */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {contactMethods.map((method, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
            >
              <Card
                hover
                interactive
                className="h-full bg-white/5 backdrop-blur-sm border-white/10 group text-center"
                motionProps={{
                  whileHover: { y: -8, scale: 1.02 },
                  transition: { type: "spring", stiffness: 300 }
                }}
              >
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${method.color} rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <method.icon className="h-8 w-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
                  {method.title}
                </h3>

                <p className="text-slate-600 dark:text-slate-300 mb-4">
                  {method.description}
                </p>

                <div className="text-primary-600 dark:text-secondary-300 font-semibold mb-2">
                  {method.contact}
                </div>

                <div className="text-slate-500 dark:text-slate-400 text-sm">
                  {method.availability}
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Office Information */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8"
        >
          {officeInfo.map((info, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-white/10 backdrop-blur-sm rounded-lg mb-4">
                <info.icon className="h-6 w-6 text-primary-600 dark:text-white" />
              </div>

              <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">
                {info.title}
              </h4>

              <div className="space-y-1">
                {info.details.map((detail, detailIndex) => (
                  <p key={detailIndex} className="text-slate-600 dark:text-slate-300 text-sm">
                    {detail}
                  </p>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default ContactHeroSection;
