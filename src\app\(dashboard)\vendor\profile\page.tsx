"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Save, User, Mail, Phone, MapPin, Building, Calendar, Camera } from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Card from "@/components/ui/Card";
import FileUpload from "@/components/ui/FileUpload";

interface VendorProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  organizationName: string;
  bio: string;
  address: string;
  city: string;
  state: string;
  country: string;
  website: string;
  avatar: File | null;
  currentAvatarUrl?: string;
  joinedDate: string;
  totalContests: number;
  totalRevenue: number;
  isVerified: boolean;
}

export default function VendorProfilePage() {
  const [profile, setProfile] = useState<VendorProfile>({
    id: "",
    name: "",
    email: "",
    phone: "",
    organizationName: "",
    bio: "",
    address: "",
    city: "",
    state: "",
    country: "Nigeria",
    website: "",
    avatar: null,
    joinedDate: "",
    totalContests: 0,
    totalRevenue: 0,
    isVerified: false
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Partial<VendorProfile>>({});

  useEffect(() => {
    // Mock data - replace with API call
    const mockProfile: VendorProfile = {
      id: "vendor_123",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+234 ************",
      organizationName: "Beauty Events Nigeria",
      bio: "Professional event organizer specializing in beauty pageants and talent shows across Nigeria. Over 10 years of experience in the entertainment industry.",
      address: "123 Victoria Island",
      city: "Lagos",
      state: "Lagos State",
      country: "Nigeria",
      website: "https://beautyevents.ng",
      avatar: null,
      currentAvatarUrl: "/api/placeholder/150/150",
      joinedDate: "2023-01-15",
      totalContests: 12,
      totalRevenue: 5420000,
      isVerified: true
    };

    setTimeout(() => {
      setProfile(mockProfile);
      setLoading(false);
    }, 1000);
  }, []);

  const handleInputChange = (field: keyof VendorProfile, value: string | File | null) => {
    setProfile(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<VendorProfile> = {};

    if (!profile.name.trim()) newErrors.name = "Name is required";
    if (!profile.email.trim()) newErrors.email = "Email is required";
    if (!profile.organizationName.trim()) newErrors.organizationName = "Organization name is required";
    if (!profile.phone.trim()) newErrors.phone = "Phone number is required";

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (profile.email && !emailRegex.test(profile.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setSaving(true);
    try {
      // TODO: Replace with actual API call
      console.log("Updating profile:", profile);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Show success message
      alert("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      alert("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Profile Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your account information and preferences
          </p>
        </div>

        {/* Profile Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">{profile.totalContests}</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Contests</div>
          </Card>
          <Card className="p-6 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              ₦{profile.totalRevenue.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</div>
          </Card>
          <Card className="p-6 text-center">
            <div className="flex items-center justify-center gap-2">
              <div className={`w-3 h-3 rounded-full ${profile.isVerified ? 'bg-green-500' : 'bg-gray-400'}`}></div>
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {profile.isVerified ? 'Verified' : 'Unverified'}
              </div>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Account Status</div>
          </Card>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Profile Picture */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <Camera className="w-5 h-5" />
              Profile Picture
            </h2>
            
            <div className="flex items-start gap-6">
              {profile.currentAvatarUrl && !profile.avatar && (
                <div className="flex-shrink-0">
                  <img 
                    src={profile.currentAvatarUrl} 
                    alt="Current avatar" 
                    className="w-24 h-24 rounded-full object-cover border-4 border-gray-200 dark:border-gray-600"
                  />
                </div>
              )}
              
              <div className="flex-1">
                <FileUpload
                  label=""
                  onFileSelect={(file) => handleInputChange("avatar", file)}
                  selectedFile={profile.avatar}
                  accept="image/*"
                  maxSize={5}
                />
              </div>
            </div>
          </Card>

          {/* Personal Information */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <User className="w-5 h-5" />
              Personal Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="Full Name"
                placeholder="Enter your full name"
                value={profile.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                error={errors.name}
                required
              />
              
              <Input
                label="Email Address"
                type="email"
                placeholder="Enter your email"
                value={profile.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                error={errors.email}
                required
              />
              
              <Input
                label="Phone Number"
                placeholder="+234 ************"
                value={profile.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                error={errors.phone}
                required
              />
              
              <Input
                label="Organization Name"
                placeholder="Enter organization name"
                value={profile.organizationName}
                onChange={(e) => handleInputChange("organizationName", e.target.value)}
                error={errors.organizationName}
                required
              />
            </div>
            
            <div className="mt-6">
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Bio
              </label>
              <textarea
                placeholder="Tell us about yourself and your organization..."
                value={profile.bio}
                onChange={(e) => handleInputChange("bio", e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </Card>

          {/* Contact Information */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Contact Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Input
                  label="Address"
                  placeholder="Enter your address"
                  value={profile.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                />
              </div>
              
              <Input
                label="City"
                placeholder="Enter city"
                value={profile.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
              />
              
              <Input
                label="State"
                placeholder="Enter state"
                value={profile.state}
                onChange={(e) => handleInputChange("state", e.target.value)}
              />
              
              <Input
                label="Country"
                placeholder="Enter country"
                value={profile.country}
                onChange={(e) => handleInputChange("country", e.target.value)}
              />
              
              <Input
                label="Website"
                placeholder="https://yourwebsite.com"
                value={profile.website}
                onChange={(e) => handleInputChange("website", e.target.value)}
              />
            </div>
          </Card>

          {/* Account Information */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Account Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Member Since
                </label>
                <div className="px-3 py-2 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white">
                  {new Date(profile.joinedDate).toLocaleDateString()}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Account Status
                </label>
                <div className="px-3 py-2 bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${profile.isVerified ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                    <span className="text-gray-900 dark:text-white">
                      {profile.isVerified ? 'Verified Account' : 'Unverified Account'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              variant="gradient" 
              isLoading={saving}
              loadingText="Saving..."
              leftIcon={<Save className="w-4 h-4" />}
            >
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
