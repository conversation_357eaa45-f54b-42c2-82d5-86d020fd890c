"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Heart, ThumbsUp, Star, Trophy, TrendingUp } from "lucide-react";

interface VoteCounterProps {
  initialCount?: number;
  maxCount?: number;
  variant?: "heart" | "thumbs" | "star" | "trophy";
  size?: "sm" | "md" | "lg" | "xl";
  animated?: boolean;
  showProgress?: boolean;
  disabled?: boolean;
  onVote?: (count: number) => void;
  className?: string;
}

const VoteCounter: React.FC<VoteCounterProps> = ({
  initialCount = 0,
  maxCount = 1000,
  variant = "heart",
  size = "md",
  animated = true,
  showProgress = false,
  disabled = false,
  onVote,
  className = "",
}) => {
  const [count, setCount] = useState<number>(initialCount);
  const [hasVoted, setHasVoted] = useState<boolean>(false);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);

  useEffect(() => {
    setCount(initialCount);
  }, [initialCount]);

  const handleVote = (): void => {
    if (disabled || isAnimating) return;

    setIsAnimating(true);
    const newCount = hasVoted ? count - 1 : count + 1;
    setCount(newCount);
    setHasVoted(!hasVoted);
    onVote?.(newCount);

    setTimeout(() => setIsAnimating(false), 300);
  };

  const formatCount = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getIcon = () => {
    const icons = {
      heart: Heart,
      thumbs: ThumbsUp,
      star: Star,
      trophy: Trophy,
    };
    return icons[variant];
  };

  const Icon = getIcon();

  const sizeClasses = {
    sm: {
      container: "p-2",
      icon: "h-4 w-4",
      text: "text-sm",
      progress: "h-1",
    },
    md: {
      container: "p-3",
      icon: "h-5 w-5",
      text: "text-base",
      progress: "h-1.5",
    },
    lg: {
      container: "p-4",
      icon: "h-6 w-6",
      text: "text-lg",
      progress: "h-2",
    },
    xl: {
      container: "p-5",
      icon: "h-8 w-8",
      text: "text-xl",
      progress: "h-2.5",
    },
  };

  const currentSize = sizeClasses[size];

  const buttonVariants = {
    initial: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 },
    voted: {
      scale: [1, 1.2, 1],
      transition: { duration: 0.3 },
    },
  };

  const iconVariants = {
    initial: { 
      scale: 1, 
      rotate: 0,
      fill: hasVoted ? "currentColor" : "none"
    },
    hover: { 
      scale: 1.1, 
      rotate: hasVoted ? 0 : 5,
    },
    voted: {
      scale: [1, 1.3, 1],
      rotate: [0, 10, 0],
      fill: "currentColor",
      transition: { duration: 0.3 },
    },
  };

  const countVariants = {
    initial: { y: 0, opacity: 1 },
    increment: {
      y: [-20, 0],
      opacity: [0, 1],
      transition: { duration: 0.3 },
    },
    decrement: {
      y: [20, 0],
      opacity: [0, 1],
      transition: { duration: 0.3 },
    },
  };

  const progressPercentage = Math.min((count / maxCount) * 100, 100);

  return (
    <div className={`inline-flex flex-col items-center space-y-2 ${className}`}>
      {/* Vote Button */}
      <motion.button
        onClick={handleVote}
        disabled={disabled}
        className={`
          inline-flex items-center space-x-2 rounded-lg
          ${currentSize.container}
          ${hasVoted 
            ? "bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400" 
            : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
          }
          ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
          transition-colors duration-200
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
        `}
        variants={buttonVariants}
        initial="initial"
        whileHover={!disabled ? "hover" : "initial"}
        whileTap={!disabled ? "tap" : "initial"}
        animate={isAnimating ? "voted" : "initial"}
      >
        {/* Icon */}
        <motion.div
          variants={iconVariants}
          initial="initial"
          whileHover={!disabled ? "hover" : "initial"}
          animate={hasVoted ? "voted" : "initial"}
        >
          <Icon 
            className={`${currentSize.icon} ${hasVoted ? "fill-current" : ""}`}
            strokeWidth={hasVoted ? 0 : 2}
          />
        </motion.div>

        {/* Count */}
        <div className="relative overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.span
              key={count}
              variants={countVariants}
              initial="initial"
              animate={animated && isAnimating ? (hasVoted ? "increment" : "decrement") : "initial"}
              className={`${currentSize.text} font-medium`}
            >
              {formatCount(count)}
            </motion.span>
          </AnimatePresence>
        </div>
      </motion.button>

      {/* Progress Bar */}
      {showProgress && (
        <div className="w-full max-w-24">
          <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full ${currentSize.progress}`}>
            <motion.div
              className={`bg-gradient-to-r from-primary-500 to-secondary-500 ${currentSize.progress} rounded-full`}
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span>0</span>
            <span>{formatCount(maxCount)}</span>
          </div>
        </div>
      )}

      {/* Additional Stats (if needed) */}
      {count > 100 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400"
        >
          <TrendingUp className="h-3 w-3" />
          <span>Trending</span>
        </motion.div>
      )}
    </div>
  );
};

// Preset Vote Counter Components
export const HeartCounter: React.FC<Omit<VoteCounterProps, "variant">> = (props) => (
  <VoteCounter {...props} variant="heart" />
);

export const ThumbsCounter: React.FC<Omit<VoteCounterProps, "variant">> = (props) => (
  <VoteCounter {...props} variant="thumbs" />
);

export const StarCounter: React.FC<Omit<VoteCounterProps, "variant">> = (props) => (
  <VoteCounter {...props} variant="star" />
);

export const TrophyCounter: React.FC<Omit<VoteCounterProps, "variant">> = (props) => (
  <VoteCounter {...props} variant="trophy" />
);

export default VoteCounter;
