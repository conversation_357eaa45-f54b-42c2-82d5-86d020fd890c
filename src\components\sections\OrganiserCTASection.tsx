"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, Users, Trophy, Zap, CheckCircle, Star } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const OrganiserCTASection: React.FC = () => {
  const benefits = [
    {
      icon: Users,
      title: "Grow Your Community",
      description: "Engage your audience with interactive contests that build lasting connections.",
    },
    {
      icon: Trophy,
      title: "Celebrate Excellence",
      description: "Recognize and reward outstanding achievements with professional award ceremonies.",
    },
    {
      icon: Zap,
      title: "Save Time & Effort",
      description: "Automate complex processes and focus on what matters most - your participants.",
    },
  ];

  const features = [
    "Free 14-day trial",
    "No setup fees",
    "24/7 priority support",
    "Custom branding",
    "Advanced analytics",
    "API access",
  ];

  const testimonial = {
    quote: "Kontestica transformed our annual awards. The platform is intuitive, secure, and our engagement increased by 300%. Best investment we've made!",
    author: "<PERSON>",
    role: "Event Director",
    company: "TechStart Inc.",
    rating: 5,
  };

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(106,13,173,0.3)_1px,transparent_0)] bg-[size:40px_40px]" />
      </div>

      <div className="relative z-10">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="text-center max-w-4xl mx-auto mb-16"
        >
          <motion.h2
            variants={staggerItemVariants}
            className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6"
          >
            <span className="text-text-light dark:text-text-dark">
              Ready to Become an
            </span>
            <br />
            <span className="bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent">
              Organiser?
            </span>
          </motion.h2>

          <motion.p
            variants={staggerItemVariants}
            className="text-xl lg:text-2xl text-gray-600 dark:text-gray-400 mb-8 leading-relaxed"
          >
            Join thousands of successful organizers who trust Kontestica to power 
            their contests, awards, and community engagement initiatives.
          </motion.p>
        </motion.div>

        {/* Benefits Grid */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
            >
              <Card
                hover
                className="text-center h-full group"
                motionProps={{
                  whileHover: { y: -8 },
                  transition: { type: "spring", stiffness: 300 }
                }}
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <benefit.icon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                </div>
                
                <h3 className="text-xl font-bold text-text-light dark:text-text-dark mb-4">
                  {benefit.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {benefit.description}
                </p>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Testimonial */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <Card
            size="lg"
            className="max-w-4xl mx-auto text-center bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20"
          >
            {/* Rating */}
            <div className="flex justify-center mb-4">
              {[...Array(testimonial.rating)].map((_, i) => (
                <Star
                  key={i}
                  className="h-5 w-5 text-yellow-400 fill-current"
                />
              ))}
            </div>

            {/* Quote */}
            <blockquote className="text-xl lg:text-2xl text-gray-700 dark:text-gray-300 mb-6 leading-relaxed italic">
              &ldquo;{testimonial.quote}&rdquo;
            </blockquote>

            {/* Author */}
            <div className="flex items-center justify-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-full flex items-center justify-center">
                <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                  {testimonial.author.charAt(0)}
                </span>
              </div>
              <div className="text-left">
                <div className="font-semibold text-text-light dark:text-text-dark">
                  {testimonial.author}
                </div>
                <div className="text-gray-600 dark:text-gray-400">
                  {testimonial.role}, {testimonial.company}
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Features & CTA */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="max-w-3xl mx-auto text-center"
        >
          {/* Features List */}
          <motion.div
            variants={staggerItemVariants}
            className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="flex items-center space-x-2 text-gray-600 dark:text-gray-400"
                whileHover={{ scale: 1.05 }}
              >
                <CheckCircle className="h-5 w-5 text-primary-500 flex-shrink-0" />
                <span className="font-medium">{feature}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            variants={staggerItemVariants}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-8"
          >
            <Button
              size="xl"
              rightIcon={<ArrowRight className="h-6 w-6" />}
              className="px-10 py-4 text-lg font-semibold"
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 },
              }}
            >
              Start Free Trial
            </Button>
            
            <Button
              variant="outline"
              size="xl"
              className="px-10 py-4 text-lg font-semibold"
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 },
              }}
            >
              Schedule Demo
            </Button>
          </motion.div>

          {/* Bottom Note */}
          <motion.p
            variants={staggerItemVariants}
            className="text-gray-500 dark:text-gray-400 text-sm"
          >
            No credit card required • Cancel anytime • 
            <span className="text-primary-600 dark:text-primary-400 font-medium"> 99.9% uptime guarantee</span>
          </motion.p>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default OrganiserCTASection;
