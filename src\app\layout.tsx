import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AOSProvider from "@/components/providers/AOSProvider";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/components/ui/Toast";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Kontestica - Celebrate Winners. Power Contests.",
  description: "Modern contest and award voting platform. Create contests, manage nominations, and conduct secure voting with real-time analytics.",
  keywords: ["contest", "voting", "awards", "competition", "nomination", "platform"],
  authors: [{ name: "Kontestica Team" }],
  creator: "Kontestica",
  publisher: "Kontestica",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://kontestica.com"),
  openGraph: {
    title: "Kontestica - Celebrate Winners. Power Contests.",
    description: "Modern contest and award voting platform with secure voting and real-time analytics.",
    url: "https://kontestica.com",
    siteName: "Kontestica",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Kontestica - Celebrate Winners. Power Contests.",
    description: "Modern contest and award voting platform with secure voting and real-time analytics.",
    creator: "@kontestica",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <ToastProvider>
              <AOSProvider>
                {children}
              </AOSProvider>
            </ToastProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
