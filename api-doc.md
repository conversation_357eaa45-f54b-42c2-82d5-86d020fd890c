# 📘 Client Authentication API Documentation

**Base Route:** `/api/auth`

---

## 📥 Register Client

**Route:** `POST /api/auth/register`

Registers a new client with uploaded logo.

### Headers

```
Content-Type: multipart/form-data
```

### Request Body (Form Data)

| Field          | Type   | Required | Description                     |
|----------------|--------|----------|---------------------------------|
| `name`         | string | ✅        | Full name of the client         |
| `email`        | string | ✅        | Unique client email address     |
| `password`     | string | ✅        | Client's password               |
| `bio`          | string | ✅        | Short biography of the client   |
| `facebookUrl`  | string | ❌        | Facebook profile URL            |
| `instagramUrl` | string | ❌        | Instagram profile URL           |
| `tweeterurl`   | string | ❌        | Twitter (X) profile URL         |
| `file`         | file   | ✅        | Logo image (form field: `file`) |

### Success Response

```json
{
  "success": true,
  "message": "Client registered successfully"
}
```

---

## 🔐 Login Client

**Route:** `POST /api/auth/login`

Logs in a client using email and password.

### Request Body

```json
{
  "email": "<EMAIL>",
  "password": "securePassword"
}
```

### Success Response

```json
{
  "success": true,
  "message": "Client logged in successfully",
  "token": "JWT_TOKEN"
}
```

---

## 🚪 Logout Client

**Route:** `POST /api/auth/logout`

Logs out the currently authenticated client.

### Success Response

```json
{
  "success": true,
  "message": "Client logged out successfully"
}
```

---

## ✅ Verify Email

**Route:** `POST /api/auth/verify-email`

Verifies a client's email via OTP.

### Request Query

| Parameter | Type   | Required | Description               |
|-----------|--------|----------|---------------------------|
| `otp`     | string | ✅        | One-Time Password (OTP)   |
| `email`   | string | ✅        | Client's email address    |

### Example

```
POST /api/auth/verify-email?otp=123456&email=<EMAIL>
```

### Success Response

```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

---

## 🔁 Request Password Reset

**Route:** `POST /api/auth/send-reset-otp`

Sends a password reset OTP to the client's email.

### Request Body

```json
{
  "email": "<EMAIL>"
}
```

### Success Response

```json
{
  "success": true,
  "message": "Password reset email sent successfully"
}
```

---

## 🔑 Reset Password

**Route:** `POST /api/auth/reset-password`

Resets a client's password using the provided reset OTP.

### Request Body

```json
{
  "email": "<EMAIL>",
  "newPassword": "newSecurePassword",
  "resetOtp": "123456"
}
```

### Success Response

```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

---



### Award API Documentation

#### Base Route: `/api/awards`

---

#### `GET /api/awards/me`

- **Description**: Get all awards created by the authenticated client.
- **Access**: Client (Requires Bearer Token)
- **Responses**:
  - `200 OK`: List of client awards.
  - `500 Internal Server Error`: Server error.

---

#### `GET /api/awards/`

- **Description**: Get all awards (public/admin view).
- **Access**: Public
- **Responses**:
  - `200 OK`: List of all awards.
  - `500 Internal Server Error`: Server error.

---

#### `GET /api/awards/:id`

- **Description**: Get award by ID.
- **Access**: Client (only if it belongs to the client)
- **Responses**:
  - `200 OK`: Award object.
  - `404 Not Found`: Award not found.

---

#### `GET /api/awards/search/:slug`

- **Description**: Search for award by slug.
- **Access**: Public
- **Responses**:
  - `200 OK`: Award object.
  - `404 Not Found`: Award not found.

---

#### `POST /api/awards/`

- **Description**: Create a new award.
- **Access**: Client (Requires Bearer Token)
- **Body**: `multipart/form-data`
  - Required fields:
    - `title`: string
    - `description`: string
    - `registrationFee`: number
    - `registrationStartDate`: string (ISO date)
    - `registrationCloseDate`: string (ISO date)
    - `votingStartDate`: string (ISO date)
    - `votingCloseDate`: string (ISO date)
    - `startsAt`: string (ISO date)
    - `endsAt`: string (ISO date)
    - `numberOfWinners`: number
    - `banner`: image (file)
- **Responses**:
  - `200 OK`: Award created.
  - `400 Bad Request`: Validation failed.
  - `500 Internal Server Error`: Server error.

---

#### `PATCH /api/awards/:id`

- **Description**: Update an existing award.
- **Access**: Client (Requires Bearer Token)
- **Body**: `multipart/form-data`
  - Accepts same fields as creation endpoint.
- **Responses**:
  - `200 OK`: Award updated.
  - `404 Not Found`: Award not found or unauthorized.
  - `500 Internal Server Error`: Server error.

---

#### `DELETE /api/awards/:id`

- **Description**: Delete an award.
- **Access**: Client (Requires Bearer Token)
- **Responses**:
  - `200 OK`: Award deleted.
  - `404 Not Found`: Award not found or unauthorized.
  - `500 Internal Server Error`: Server error.

---

### Notes:

- All authenticated routes require `Authorization: Bearer <token>` in headers.
- Ensure that image uploads use `multipart/form-data` content-type.
- Date comparisons and validations are enforced on the server.





# Contestant API Documentation

This API provides endpoints for managing contestants in contests.

## Base URL
`/api/contestants`

---

### Register a Contestant

**POST** `/`

Registers a new contestant. Requires image upload.

**Request Body (multipart/form-data):**
- `name`: string (required)
- `stageName`: string (required)
- `email`: string (required)
- `phone`: string (required)
- `bio`: string (optional)
- `contestID`: string (required)
- `paymentReference`: string (required)
- `contestantphoto`: file (optional)

**Responses:**
- `201`: Contestant registered successfully
- `400`: Validation error or payment not verified
- `500`: Server error

---

### Get All Contestants

**GET** `/`

Fetch all contestants. Requires authentication.

**Query Params (optional):**
- `page`: number (default: 1)
- `limit`: number (default: 10)

**Responses:**
- `200`: Contestants fetched successfully
- `500`: Server error

---

### Get Contestants by Contest ID

**GET** `/contestants/:contestID`

Fetch contestants registered for a particular contest.

**Query Params (optional):**
- `status`: "approved" | "pending" | "rejected"
- `page`: number
- `limit`: number

**Responses:**
- `200`: Contestants fetched successfully
- `500`: Server error

---

### Get Contestant by ID

**GET** `/:id`

Fetch a single contestant by ID.

**Responses:**
- `200`: Contestant fetched successfully
- `404`: Contestant not found

---

### Get Contestants by Stage Name

**GET** `/stage-name/:stageName`

Fetch contestants by their stage name (case-insensitive).

**Responses:**
- `200`: Contestants fetched successfully
- `404`: No contestants found

---

### Update a Contestant

**PUT** `/:id`

Update contestant details. Can include image update.

**Body:** multipart/form-data

**Responses:**
- `200`: Contestant updated successfully
- `404`: Contestant not found
- `500`: Server error

---

### Update Contestant Status

**PUT** `/:id/status`

Update the status of a contestant. Only contest owner can perform this.

**Body:**
- `status`: "approved" | "rejected" | "pending"

**Responses:**
- `200`: Status updated successfully
- `400`: Invalid status
- `403`: Unauthorized
- `404`: Contestant not found

---

### Delete a Contestant

**DELETE** `/:id`

Deletes a contestant and their uploaded profile picture.

**Responses:**
- `200`: Contestant deleted successfully
- `404`: Contestant not found
- `500`: Server error



# 📦 Contest API Documentation

This file documents the RESTful API routes and controller behavior for managing contests in the system.

---

## 🛤️ Routes Summary

| Method | Endpoint                | Description                            |
|--------|-------------------------|----------------------------------------|
| GET    | /client-contests        | Fetch all contests by logged-in client |
| GET    | /                       | Fetch all contests (admin/public)      |
| GET    | /slug/:slug             | Fetch contest by slug                  |
| GET    | /:id                    | Fetch contest by ID                    |
| POST   | /                       | Create a new contest                   |
| PUT    | /:id                    | Update contest                         |
| DELETE | /:id                    | Delete contest                         |
| PATCH  | /approve/:id            | Approve a contest                      |

---

## ✨ Endpoints Details

### GET `/client-contests`
**Description:** Get all contests for the authenticated client.  
**Auth Required:** ✅ Yes (Client Token)

---

### GET `/`
**Description:** Get all contests (for admin/public use).  
**Auth Required:** ❌ No

---

### GET `/slug/:slug`
**Description:** Retrieve a contest using its unique slug.  
**Auth Required:** ❌ No

---

### GET `/:id`
**Description:** Get a contest by its MongoDB ID.  
**Auth Required:** ❌ No

---

### POST `/`
**Description:** Create a new contest.  
**Auth Required:** ✅ Yes (Client Token)  
**Content-Type:** multipart/form-data

#### Fields:
- `title` (string, required)
- `description` (string, required)
- `votePrice` (number, required)
- `registrationFee` (number)
- `registrationStartDate` (date)
- `registrationCloseDate` (date)
- `votingStartDate` (date)
- `votingCloseDate` (date)
- `startsAt` (date, required)
- `endsAt` (date, required)
- `requireApproval` (boolean)
- `numberOfWinners` (number)
- `banner` (file, optional)
- `logo` (file, optional)

---

### PUT `/:id`
**Description:** Update an existing contest.  
**Auth Required:** ✅ Yes (Client Token)  
**Content-Type:** multipart/form-data

#### Fields:
- `title` (string, optional)
- `description` (string, optional)
- `votePrice` (number, optional)
- `banner` (file, optional)
- `logo` (file, optional)

---

### DELETE `/:id`
**Description:** Delete a contest by its ID.  
**Auth Required:** ✅ Yes (Client Token)

---

### PATCH `/approve/:id`
**Description:** Approve a contest (admin functionality).  
**Auth Required:** ❌ (Ideally should be admin-only)

---

## 🔐 Middleware
- `isClient` — Verifies that the request is from an authenticated client.

## 📁 File Upload
- `uploadContestImage.ts` handles banner/logo uploads via Multer and Cloudinary.

---

© 2025 Contest Platform API
