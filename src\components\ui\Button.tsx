"use client";

import { forwardRef, ButtonHTMLAttributes, ReactNode } from "react";
import { motion, MotionProps } from "framer-motion";
import { Loader2 } from "lucide-react";

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "danger" | "gradient";
  size?: "sm" | "md" | "lg" | "xl";
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  fullWidth?: boolean;
  glow?: boolean;
  children: ReactNode;
  motionProps?: MotionProps;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      isLoading = false,
      loadingText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      glow = false,
      children,
      className = "",
      disabled,
      motionProps,
      ...props
    },
    ref
  ) => {
    const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden";

    const variantClasses = {
      primary: "bg-gradient-to-r from-primary-600 to-primary-500 hover:from-primary-700 hover:to-primary-600 text-white focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-black shadow-lg hover:shadow-xl transition-all duration-200",
      secondary: "bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white focus:ring-secondary-500 focus:ring-offset-white dark:focus:ring-offset-black shadow-lg hover:shadow-xl transition-all duration-200",
      outline: "border-2 border-primary-500 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:border-primary-600 dark:hover:border-primary-400 focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-black",
      ghost: "text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-black",
      danger: "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white focus:ring-red-500 focus:ring-offset-white dark:focus:ring-offset-black shadow-lg hover:shadow-xl",
      gradient: "bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-500 hover:from-primary-700 hover:via-primary-600 hover:to-secondary-600 text-white focus:ring-primary-500 focus:ring-offset-white dark:focus:ring-offset-black shadow-xl hover:shadow-2xl transition-all duration-200 bg-[length:200%_auto] animate-gradient-shift",
    };

    const sizeClasses = {
      sm: "px-3 py-1.5 text-sm",
      md: "px-4 py-2 text-base",
      lg: "px-6 py-3 text-lg",
      xl: "px-8 py-4 text-xl",
    };

    const iconSizes = {
      sm: "h-4 w-4",
      md: "h-5 w-5",
      lg: "h-6 w-6",
      xl: "h-7 w-7",
    };

    const buttonClasses = `
      ${baseClasses}
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${fullWidth ? "w-full" : ""}
      ${glow ? (variant === "primary" || variant === "gradient" ? "shadow-orange-glow hover:shadow-orange-glow-lg" : variant === "secondary" ? "shadow-red-glow hover:shadow-red-glow-lg" : "") : ""}
      ${className}
    `.trim();

    const buttonVariants = {
      initial: {
        scale: 1,
        y: 0,
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
      },
      hover: {
        scale: 1.05,
        y: -3,
        boxShadow: variant === "gradient" || variant === "primary"
          ? "0 20px 25px -5px rgba(249, 115, 22, 0.4), 0 10px 10px -5px rgba(249, 115, 22, 0.04)"
          : variant === "secondary"
          ? "0 20px 25px -5px rgba(239, 68, 68, 0.4), 0 10px 10px -5px rgba(239, 68, 68, 0.04)"
          : "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 10,
          duration: 0.2
        }
      },
      tap: {
        scale: 0.95,
        y: 0,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 15,
          duration: 0.1
        }
      },
    };

    const content = (
      <>
        {isLoading ? (
          <>
            <Loader2 className={`${iconSizes[size]} animate-spin mr-2`} />
            {loadingText || "Loading..."}
          </>
        ) : (
          <>
            {leftIcon && (
              <span className={`${iconSizes[size]} mr-2 flex-shrink-0`}>
                {leftIcon}
              </span>
            )}
            <span className="flex-1">{children}</span>
            {rightIcon && (
              <span className={`${iconSizes[size]} ml-2 flex-shrink-0`}>
                {rightIcon}
              </span>
            )}
          </>
        )}
      </>
    );

    if (motionProps) {
      return (
        <motion.button
          ref={ref}
          className={buttonClasses}
          disabled={disabled || isLoading}
          variants={buttonVariants as any}
          initial="initial"
          whileHover="hover"
          whileTap="tap"
          {...motionProps}
          {...(props as any)}
        >
          {content}
        </motion.button>
      );
    }

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || isLoading}
        {...props}
      >
        {content}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
