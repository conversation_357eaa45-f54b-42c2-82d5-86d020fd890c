"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, HelpCircle, ArrowRight } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { Section<PERSON>eader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: "general" | "technical" | "billing" | "features";
}

const FAQSection: React.FC = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>("general");

  const faqs: FAQ[] = [
    {
      id: 1,
      question: "How do I create my first contest?",
      answer: "Creating your first contest is easy! Simply sign up for a free account, click 'Create Contest', and follow our step-by-step wizard. You can set up categories, rules, voting criteria, and customize the look and feel to match your brand.",
      category: "general",
    },
    {
      id: 2,
      question: "Is voting secure and fraud-proof?",
      answer: "Absolutely! We use enterprise-grade security measures including IP tracking, device fingerprinting, and behavioral analysis to prevent fraudulent voting. All votes are encrypted and stored securely with complete audit trails.",
      category: "technical",
    },
    {
      id: 3,
      question: "What payment methods do you accept?",
      answer: "We accept all major credit cards, PayPal, and bank transfers. For enterprise customers, we also offer invoice billing and custom payment terms. All transactions are processed securely through our PCI-compliant payment system.",
      category: "billing",
    },
    {
      id: 4,
      question: "Can I customize the contest appearance?",
      answer: "Yes! You can fully customize your contest with your own branding, colors, logos, and custom domains. Our white-label solution allows you to create a seamless experience that matches your organization's brand identity.",
      category: "features",
    },
    {
      id: 5,
      question: "Do you offer customer support?",
      answer: "We provide 24/7 email support and live chat for all users. Premium and enterprise customers also get priority phone support and dedicated account managers. Our average response time is under 2 hours.",
      category: "general",
    },
    {
      id: 6,
      question: "Can I integrate Kontestica with my existing tools?",
      answer: "Yes! We offer a comprehensive REST API and webhooks for custom integrations. We also have pre-built integrations with popular tools like Slack, Zapier, and various CRM systems.",
      category: "technical",
    },
  ];

  const categories = [
    { id: "general", label: "General", count: faqs.filter(f => f.category === "general").length },
    { id: "technical", label: "Technical", count: faqs.filter(f => f.category === "technical").length },
    { id: "billing", label: "Billing", count: faqs.filter(f => f.category === "billing").length },
    { id: "features", label: "Features", count: faqs.filter(f => f.category === "features").length },
  ];

  const filteredFAQs = faqs.filter(faq => faq.category === activeCategory);

  const toggleFAQ = (id: number): void => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
    >
      <SectionHeader>
        <SectionTitle gradient>Frequently Asked Questions</SectionTitle>
        <SectionSubtitle>
          Find answers to common questions about Kontestica. 
          Can&apos;t find what you&apos;re looking for? Contact our support team.
        </SectionSubtitle>
      </SectionHeader>

      <div className="max-w-4xl mx-auto">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          {/* Category Tabs */}
          <motion.div
            variants={staggerItemVariants}
            className="flex flex-wrap justify-center gap-2 mb-12"
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  activeCategory === category.id
                    ? "bg-primary-500 text-white shadow-lg"
                    : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
              >
                {category.label}
                <span className="ml-2 text-xs opacity-75">({category.count})</span>
              </button>
            ))}
          </motion.div>

          {/* FAQ Accordion */}
          <motion.div variants={staggerItemVariants} className="space-y-4">
            <AnimatePresence>
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={`${activeCategory}-${faq.id}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="overflow-hidden">
                    <button
                      onClick={() => toggleFAQ(faq.id)}
                      className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
                          <HelpCircle className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-text-light dark:text-text-dark">
                          {faq.question}
                        </h3>
                      </div>
                      <motion.div
                        animate={{ rotate: openFAQ === faq.id ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                        className="flex-shrink-0"
                      >
                        <ChevronDown className="h-5 w-5 text-gray-500" />
                      </motion.div>
                    </button>

                    <AnimatePresence>
                      {openFAQ === faq.id && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 pl-20">
                            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>

          {/* Still Have Questions CTA */}
          <motion.div
            variants={staggerItemVariants}
            className="text-center mt-16"
          >
            <Card
              size="lg"
              className="bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20"
            >
              <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
                Still Have Questions?
              </h3>
              <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
                Our support team is here to help. Get in touch and we&apos;ll respond within 2 hours.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <Button
                  size="lg"
                  rightIcon={<ArrowRight className="h-5 w-5" />}
                  motionProps={{
                    whileHover: { scale: 1.05 },
                    whileTap: { scale: 0.95 }
                  }}
                >
                  Contact Support
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  motionProps={{
                    whileHover: { scale: 1.05 },
                    whileTap: { scale: 0.95 }
                  }}
                >
                  Browse Help Center
                </Button>
              </div>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default FAQSection;
