/* Import Poppins font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Orange & Red Theme CSS Variables */
:root {
  /* Light Mode Colors */
  --background-light: #ffffff;
  --background-light-secondary: #fef7f0;
  --background-light-elevated: #fef2e8;
  --text-light-primary: #1a1a1a;
  --text-light-secondary: #4a4a4a;
  --text-light-tertiary: #6a6a6a;

  /* Dark Mode Colors */
  --background-dark: #000000;
  --background-dark-secondary: #0a0a0a;
  --background-dark-elevated: #1a1a1a;
  --text-dark-primary: #ffffff;
  --text-dark-secondary: #e5e5e5;
  --text-dark-tertiary: #a3a3a3;

  /* Primary Orange Colors */
  --primary-light: #fb923c;
  --primary-mid: #f97316;
  --primary-dark: #c2410c;
  --primary-darker: #9a3412;

  /* Secondary Red Colors */
  --secondary-light: #f87171;
  --secondary-mid: #ef4444;
  --secondary-dark: #b91c1c;
  --secondary-darker: #991b1b;

  /* Yellow-Orange Accent */
  --accent-light: #fbbf24;
  --accent-mid: #f59e0b;
  --accent-dark: #b45309;

  /* Status Colors */
  --success-light: #10b981;
  --success-dark: #34d399;
  --error-light: #ef4444;
  --error-dark: #f87171;

  /* Modern Gradients */
  --gradient-orange-red: linear-gradient(135deg, #f97316, #ef4444);
  --gradient-orange-yellow: linear-gradient(135deg, #c2410c, #f59e0b);
  --gradient-red-orange: linear-gradient(135deg, #b91c1c, #ea580c);
  --gradient-dark: linear-gradient(135deg, #000000, #1a0a0a, #2a1a1a);

  /* Shadows */
  --shadow-light: rgba(67, 20, 7, 0.1);
  --shadow-medium: rgba(67, 20, 7, 0.15);
  --shadow-heavy: rgba(67, 20, 7, 0.25);
  --shadow-orange: rgba(249, 115, 22, 0.2);
  --shadow-red: rgba(239, 68, 68, 0.2);
}

/* Base Styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  letter-spacing: -0.01em;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light mode styles */
body {
  background-color: var(--background-light);
  color: var(--text-light-primary);
}

/* Dark mode styles */
body.dark {
  background-color: var(--background-dark);
  color: var(--text-dark-primary);
}

/* Subtle animated background pattern for modern feel */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.03;
  background:
    radial-gradient(circle at 25% 25%, var(--primary-mid) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, var(--secondary-mid) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  transition: opacity 0.3s ease;
}

body.dark::before {
  opacity: 0.05;
}

/* Dark mode overlay for better contrast */
body.dark::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 20%, rgba(249, 115, 22, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(239, 68, 68, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
  margin-bottom: 0.5em;
}

/* Light mode typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-light-primary);
}

/* Dark mode typography */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: var(--text-dark-primary);
}

/* Enhanced Button Animations */
.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

/* Gradient Animation */
@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-shift {
  animation: gradient-shift 3s ease infinite;
}

/* Pulse Animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

/* Float Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.3;
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary), var(--secondary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--primary-light), var(--secondary-light));
}

h1 {
  font-size: clamp(3rem, 6vw, 5.5rem);
  letter-spacing: -0.06em;
  font-weight: 700;
  line-height: 1.1;
}

h2 {
  font-size: clamp(2.25rem, 4.5vw, 4rem);
  letter-spacing: -0.05em;
  font-weight: 700;
  line-height: 1.2;
}

h3 {
  font-size: clamp(1.875rem, 3.5vw, 3.25rem);
  letter-spacing: -0.04em;
  font-weight: 600;
  line-height: 1.25;
}

h4 {
  font-size: clamp(1.5rem, 2.75vw, 2.5rem);
  letter-spacing: -0.03em;
  font-weight: 600;
  line-height: 1.3;
}

h5 {
  font-size: clamp(1.25rem, 2.25vw, 2rem);
  letter-spacing: -0.02em;
  font-weight: 500;
  line-height: 1.35;
}

h6 {
  font-size: clamp(1.125rem, 1.75vw, 1.5rem);
  letter-spacing: -0.01em;
  font-weight: 500;
  line-height: 1.4;
}

p {
  margin-bottom: 1.25rem;
  font-size: clamp(1rem, 1.25vw, 1.125rem);
  line-height: 1.75;
  letter-spacing: -0.005em;
}

/* Enhanced Typography Classes */
.text-display {
  font-family: var(--font-display);
  font-weight: 700;
  letter-spacing: -0.06em;
  line-height: 1.1;
}

.text-body {
  font-family: var(--font-sans);
  font-weight: 400;
  letter-spacing: -0.01em;
  line-height: 1.7;
}

.text-caption {
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.01em;
  line-height: 1.5;
  text-transform: uppercase;
}

/* Responsive Spacing */
.space-y-responsive > * + * {
  margin-top: clamp(1rem, 2vw, 2rem);
}

.space-y-responsive-lg > * + * {
  margin-top: clamp(1.5rem, 3vw, 3rem);
}

.space-y-responsive-xl > * + * {
  margin-top: clamp(2rem, 4vw, 4rem);
}

/* Links */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--secondary);
}

/* Selection */
::selection {
  background-color: #3b82f6;
  color: white;
}

::-moz-selection {
  background-color: #3b82f6;
  color: white;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Focus States */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Modern AI Utility Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

/* Modern Glass Effect */
.glass-modern {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-modern-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Gradient Text */
.gradient-text-modern {
  background: linear-gradient(135deg, var(--primary-mid), var(--accent-mid));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% auto;
  animation: gradientShift 8s ease-in-out infinite;
}

/* Animated Beam Effect */
.beam-effect {
  position: relative;
  overflow: hidden;
}

.beam-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.4), transparent);
  animation: shimmer 2s infinite;
}

/* Modern Card Hover Effect */
.card-modern {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.card-modern:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 20px rgba(249, 115, 22, 0.3);
}

/* Floating Animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Pulse Glow Effect */
.pulse-glow {
  animation: pulseGlow 3s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(249, 115, 22, 0.6), 0 0 40px rgba(239, 68, 68, 0.3);
  }
}

/* Modern Grid Pattern */
.grid-pattern {
  background-image:
    linear-gradient(rgba(249, 115, 22, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(249, 115, 22, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { background-position: 0 0; }
  100% { background-position: 50px 50px; }
}
