"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  Users,
  Vote,
  DollarSign,
  TrendingUp,
  Calendar,
  Trophy,
  Eye,
  Download,
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Activity,
  Clock,
  Star,
  Target
} from "lucide-react";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { ContestsService } from "@/lib/api/contests";
import { ContestantsService } from "@/lib/api/contestants";

interface ContestAnalytics {
  contest: {
    _id: string;
    title: string;
    status: string;
    registrationFee: number;
    votePrice: number;
    startsAt: string;
    endsAt: string;
  };
  stats: {
    totalContestants: number;
    totalVotes: number;
    totalRevenue: number;
    registrationRevenue: number;
    votingRevenue: number;
    averageVotesPerContestant: number;
    topContestantVotes: number;
  };
  contestants: Array<{
    _id: string;
    name: string;
    stageName: string;
    voteCount: number;
    registrationDate: string;
    status: string;
  }>;
  votingTrends: Array<{
    date: string;
    votes: number;
    revenue: number;
  }>;
}

export default function ContestAnalyticsPage() {
  const params = useParams();
  const router = useRouter();
  const contestId = params.id as string;

  const [analytics, setAnalytics] = useState<ContestAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | 'all'>('30d');

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch contest details
        const contestResponse = await ContestsService.getContestById(contestId);
        
        if (!contestResponse.success || !contestResponse.contest) {
          setError("Contest not found");
          return;
        }

        // Fetch contestants
        const contestantsResponse = await ContestantsService.getContestantsByContest(contestId);
        
        const contestants = contestantsResponse.success ? contestantsResponse.contestants || [] : [];
        
        // Calculate analytics
        const totalVotes = contestants.reduce((sum, contestant) => sum + (contestant.voteCount || 0), 0);
        const totalContestants = contestants.length;
        const registrationRevenue = totalContestants * contestResponse.contest.registrationFee;
        const votingRevenue = totalVotes * contestResponse.contest.votePrice;
        const totalRevenue = registrationRevenue + votingRevenue;

        // Mock voting trends data (in real app, this would come from API)
        const votingTrends = generateMockTrends(timeRange);

        const analyticsData: ContestAnalytics = {
          contest: contestResponse.contest,
          stats: {
            totalContestants,
            totalVotes,
            totalRevenue,
            registrationRevenue,
            votingRevenue,
            averageVotesPerContestant: totalContestants > 0 ? totalVotes / totalContestants : 0,
            topContestantVotes: Math.max(...contestants.map(c => c.voteCount || 0), 0)
          },
          contestants: contestants.map(c => ({
            _id: c._id,
            name: c.name,
            stageName: c.stageName,
            voteCount: c.voteCount || 0,
            registrationDate: c.createdAt || new Date().toISOString(),
            status: c.status
          })),
          votingTrends
        };

        setAnalytics(analyticsData);
      } catch (err: any) {
        console.error("Error fetching analytics:", err);
        setError("Failed to load analytics data");
      } finally {
        setLoading(false);
      }
    };

    if (contestId) {
      fetchAnalytics();
    }
  }, [contestId, timeRange]);

  const generateMockTrends = (range: string) => {
    const days = range === '7d' ? 7 : range === '30d' ? 30 : 90;
    const trends = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      trends.push({
        date: date.toISOString().split('T')[0],
        votes: Math.floor(Math.random() * 50) + 10,
        revenue: Math.floor(Math.random() * 5000) + 1000
      });
    }
    
    return trends;
  };

  const exportData = () => {
    if (!analytics) return;
    
    const csvData = [
      ['Contestant', 'Stage Name', 'Votes', 'Registration Date', 'Status'],
      ...analytics.contestants.map(c => [
        c.name,
        c.stageName,
        c.voteCount.toString(),
        new Date(c.registrationDate).toLocaleDateString(),
        c.status
      ])
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${analytics.contest.title}_analytics.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {error || "Analytics not available"}
            </h1>
            <Button onClick={() => router.push('/vendor')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              onClick={() => router.push('/vendor')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Contest Analytics
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {analytics.contest.title}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="all">All time</option>
            </select>
            
            <Button
              variant="outline"
              onClick={exportData}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Contestants</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics.stats.totalContestants}
                </p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Votes</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics.stats.totalVotes.toLocaleString()}
                </p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg">
                <Vote className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  ₦{analytics.stats.totalRevenue.toLocaleString()}
                </p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                <DollarSign className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>

        {/* Top Contestants */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Contestants
            </h3>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <Trophy className="h-4 w-4" />
              <span>Leaderboard</span>
            </div>
          </div>

          {analytics.contestants.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">No contestants yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {analytics.contestants
                .sort((a, b) => b.voteCount - a.voteCount)
                .slice(0, 10)
                .map((contestant, index) => (
                  <motion.div
                    key={contestant._id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`flex items-center justify-center w-8 h-8 rounded-full font-bold text-sm ${
                        index === 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                        index === 1 ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' :
                        index === 2 ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                        'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {contestant.stageName}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {contestant.name}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-semibold text-gray-900 dark:text-white">
                          {contestant.voteCount} votes
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          ₦{(contestant.voteCount * analytics.contest.votePrice).toLocaleString()}
                        </p>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        contestant.status === 'approved'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : contestant.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      }`}>
                        {contestant.status}
                      </div>
                    </div>
                  </motion.div>
                ))}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Avg Votes/Contestant</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {analytics.stats.averageVotesPerContestant.toFixed(1)}
                </p>
              </div>
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                <TrendingUp className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </Card>
        </div>

        {/* Revenue Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Revenue Breakdown
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-blue-500 rounded"></div>
                  <span className="text-gray-600 dark:text-gray-400">Registration Fees</span>
                </div>
                <span className="font-semibold text-gray-900 dark:text-white">
                  ₦{analytics.stats.registrationRevenue.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 bg-green-500 rounded"></div>
                  <span className="text-gray-600 dark:text-gray-400">Voting Revenue</span>
                </div>
                <span className="font-semibold text-gray-900 dark:text-white">
                  ₦{analytics.stats.votingRevenue.toLocaleString()}
                </span>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Contest Status
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">Status:</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${
                  analytics.contest.status === 'active' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                }`}>
                  {analytics.contest.status}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">Start Date:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {new Date(analytics.contest.startsAt).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600 dark:text-gray-400">End Date:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {new Date(analytics.contest.endsAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </Card>
        </div>
