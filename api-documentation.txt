AUTH
Auth routes

POST
Register
https://kontestica-backend-api.onrender.com/api/auth/register
Register a new client account.

Body
formdata
name
dev tester

email
<EMAIL>

password
********

bio
software dev & tester

file
Example Request
Register
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/auth/register' \
--form 'name="dev tester"' \
--form 'email="<EMAIL>"' \
--form 'password="********"' \
--form 'bio="software dev & tester"' \
--form 'file=@"/C:/Users/<USER>/Downloads/user.png"'
201 Created
Example Response
Body
Headers (27)
json
{
  "success": true,
  "message": "Client registered successfully"
}

POST
login
https://kontestica-backend-api.onrender.com/api/auth/login
Login a client and receive an authentication token set to reques.cookies as token

Body
urlencoded
email
<EMAIL>

password
********

Example Request
login
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/auth/login' \
--data-urlencode 'email=<EMAIL>' \
--data-urlencode 'password=********'
200 OK
Example Response
Body
Headers (28)
View More
json
{
  "success": true,
  "message": "Client logged in successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NzQzNjcwZjRjYWQ0M2MzYzk1MTY1ZSIsImlhdCI6MTc1MjQ0NjgzMCwiZXhwIjoxNzUzMDUxNjMwfQ.EXMP1E5L7hn8ILQnRwOegKoXsZirnCycX6oHVZe82Nk"
}
POST
logout
https://kontestica-backend-api.onrender.com/api/auth/logout
Logout the currently authenticated client

Example Request
logout
curl
curl --location --request POST 'https://kontestica-backend-api.onrender.com/api/auth/logout'
200 OK
Example Response
Body
Headers (28)
json
{
  "success": true,
  "message": "Client logged out successfully"
}
POST
verify email
https://kontestica-backend-api.onrender.com/api/auth/verify-email?otp=937672&email=<EMAIL>
Verify client’s email address via token

PARAMS
otp
937672

email
<EMAIL>

Example Request
verify email
View More
curl
curl --location --request POST 'https://kontestica-backend-api.onrender.com/api/auth/verify-email?otp=937672&email=devtest%40kontestica.com'
200 OK
Example Response
Body
Headers (27)
json
{
  "success": true,
  "message": "Email verified successfully"
}





POST
Create Contest
https://kontestica-backend-api.onrender.com/api/contests
Create a new contest with banner and logo uploads.

AUTHORIZATION
Bearer Token
Token
<token>

HEADERS
Accept
multipart/form-data

PARAMS
title
test contest

description
test description

registrationFee
5000

registrationStartDate
Body
formdata
title
Face of NACOS, 2025

description
Face of nacos 2025, University of Cross River State

votePrice
200

registrationFee
1000

registrationStartDate
2025-07-14T09:00:00Z

registrationCloseDate
2025-07-16T09:00:00Z

votingStartDate
2025-07-16T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
false

numberOfWinners
2

banner
Example Request
Create Contest
View More
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contests' \
--header 'Accept: multipart/form-data' \
--form 'title="Face of NACOS, 2025"' \
--form 'description="Face of nacos 2025, University of Cross River State"' \
--form 'votePrice="200"' \
--form 'registrationFee="1000"' \
--form 'registrationStartDate="2025-07-14T09:00:00Z"' \
--form 'registrationCloseDate="2025-07-16T09:00:00Z"' \
--form 'votingStartDate="2025-07-16T09:00:00Z"' \
--form 'votingCloseDate="2025-07-20T09:00:00Z"' \
--form 'startsAt="2025-07-20T09:00:00Z"' \
--form 'endsAt="2025-07-25T09:00:00Z"' \
--form 'requireApproval="false"' \
--form 'numberOfWinners="2"' \
--form 'banner=@"/C:/Users/<USER>/Downloads/oranges.jpeg"'
201 Created
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contest created",
  "contest": {
    "clientID": "68743670f4cad43c3c95165e",
    "title": "Face of NACOS, 2025",
    "description": "Face of nacos 2025, University of Cross River State",
    "votePrice": 200,
    "registrationFee": 1000,
    "registrationStartDate": "2025-07-14T09:00:00.000Z",
    "registrationCloseDate": "2025-07-16T09:00:00.000Z",
    "votingStartDate": "2025-07-16T09:00:00.000Z",
    "votingCloseDate": "2025-07-20T09:00:00.000Z",
    "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752304954/contest_banner/oranges.jpg",
    "bannerPublicId": "contest_banner/oranges",
    "startsAt": "2025-07-20T09:00:00.000Z",
    "endsAt": "2025-07-25T09:00:00.000Z",
    "acceptingRegistrations": true,
    "acceptingVotes": true,
    "requireApproval": false,
    "numberOfWinners": 2,
    "winners": [],
    "status": "draft",
    "_id": "6874383ff4cad43c3c951664",
    "createdAt": "2025-07-13T22:50:39.351Z",
    "updatedAt": "2025-07-13T22:50:39.351Z",
    "slug": "face-of-nacos-2025",
    "__v": 0
  }
}
PUT
update contest
https://kontestica-backend-api.onrender.com/api/contests/6874383ff4cad43c3c951664
Update contest details including images.

AUTHORIZATION
Bearer Token
Token
<token>

PARAMS
id
686f8e3be3c23b96c75c6d53

description
test description

registrationFee
5000

registrationStartDate
Body
formdata
title
Face of NACOS, 2025 Updated

description
Face of nacos 2025, University of Cross River State 2025

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
update contest
View More
curl
curl --location --request PUT 'https://kontestica-backend-api.onrender.com/api/contests/6874383ff4cad43c3c951664' \
--form 'title="Face of NACOS, 2025 Updated"' \
--form 'description="Face of nacos 2025, University of Cross River State 2025"' \
--form 'banner=@"/C:/Users/<USER>/Downloads/oranges.jpeg"' \
--form 'logo=@"/C:/Users/<USER>/Downloads/user.png"' \
--form 'logo=@"/C:/Users/<USER>/Downloads/apples.jpeg"'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contest updated",
  "contest": {
    "_id": "6874383ff4cad43c3c951664",
    "clientID": "68743670f4cad43c3c95165e",
    "title": "Face of NACOS, 2025 Updated",
    "description": "Face of nacos 2025, University of Cross River State 2025",
    "votePrice": 200,
    "registrationFee": 1000,
    "registrationStartDate": "2025-07-14T09:00:00.000Z",
    "registrationCloseDate": "2025-07-16T09:00:00.000Z",
    "votingStartDate": "2025-07-16T09:00:00.000Z",
    "votingCloseDate": "2025-07-20T09:00:00.000Z",
    "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752304954/contest_banner/oranges.jpg",
    "bannerPublicId": "contest_banner/oranges",
    "startsAt": "2025-07-20T09:00:00.000Z",
    "endsAt": "2025-07-25T09:00:00.000Z",
    "acceptingRegistrations": true,
    "acceptingVotes": true,
    "requireApproval": false,
    "numberOfWinners": 2,
    "winners": [],
    "status": "draft",
    "createdAt": "2025-07-13T22:50:39.351Z",
    "updatedAt": "2025-07-13T22:59:11.975Z",
    "slug": "face-of-nacos-2025",
    "__v": 0
  }
}
GET
Get Single Contest
https://kontestica-backend-api.onrender.com/api/contests/6874383ff4cad43c3c951664
Get a single contest by its ID.

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get Single Contest
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contests/6874383ff4cad43c3c951664'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contest fetched",
  "contest": {
    "_id": "6874383ff4cad43c3c951664",
    "clientID": "68743670f4cad43c3c95165e",
    "title": "Face of NACOS, 2025 Updated",
    "description": "Face of nacos 2025, University of Cross River State 2025",
    "votePrice": 200,
    "registrationFee": 1000,
    "registrationStartDate": "2025-07-14T09:00:00.000Z",
    "registrationCloseDate": "2025-07-16T09:00:00.000Z",
    "votingStartDate": "2025-07-16T09:00:00.000Z",
    "votingCloseDate": "2025-07-20T09:00:00.000Z",
    "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752304954/contest_banner/oranges.jpg",
    "bannerPublicId": "contest_banner/oranges",
    "startsAt": "2025-07-20T09:00:00.000Z",
    "endsAt": "2025-07-25T09:00:00.000Z",
    "acceptingRegistrations": true,
    "acceptingVotes": true,
    "requireApproval": false,
    "numberOfWinners": 2,
    "winners": [],
    "status": "draft",
    "createdAt": "2025-07-13T22:50:39.351Z",
    "updatedAt": "2025-07-13T22:59:11.975Z",
    "slug": "face-of-nacos-2025",
    "__v": 0
  }
}
GET
Get contest by slug
https://kontestica-backend-api.onrender.com/api/contests/slug/face-of-nacos-2025
Retrieve a contest using its unique slug.

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get contest by slug
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contests/slug/face-of-nacos-2025'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contest fetched",
  "contest": {
    "_id": "6874383ff4cad43c3c951664",
    "clientID": "68743670f4cad43c3c95165e",
    "title": "Face of NACOS, 2025 Updated",
    "description": "Face of nacos 2025, University of Cross River State 2025",
    "votePrice": 200,
    "registrationFee": 1000,
    "registrationStartDate": "2025-07-14T09:00:00.000Z",
    "registrationCloseDate": "2025-07-16T09:00:00.000Z",
    "votingStartDate": "2025-07-16T09:00:00.000Z",
    "votingCloseDate": "2025-07-20T09:00:00.000Z",
    "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752304954/contest_banner/oranges.jpg",
    "bannerPublicId": "contest_banner/oranges",
    "startsAt": "2025-07-20T09:00:00.000Z",
    "endsAt": "2025-07-25T09:00:00.000Z",
    "acceptingRegistrations": true,
    "acceptingVotes": true,
    "requireApproval": false,
    "numberOfWinners": 2,
    "winners": [],
    "status": "draft",
    "createdAt": "2025-07-13T22:50:39.351Z",
    "updatedAt": "2025-07-13T22:59:11.975Z",
    "slug": "face-of-nacos-2025",
    "__v": 0
  }
}
GET
Get All Contest
https://kontestica-backend-api.onrender.com/api/contests/
Public route to fetch all available contests.

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get All Contest
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contests/'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contests fetched",
  "contests": [
    {
      "_id": "6874383ff4cad43c3c951664",
      "clientID": "68743670f4cad43c3c95165e",
      "title": "Face of NACOS, 2025 Updated",
      "description": "Face of nacos 2025, University of Cross River State 2025",
      "votePrice": 200,
      "registrationFee": 1000,
      "registrationStartDate": "2025-07-14T09:00:00.000Z",
      "registrationCloseDate": "2025-07-16T09:00:00.000Z",
      "votingStartDate": "2025-07-16T09:00:00.000Z",
      "votingCloseDate": "2025-07-20T09:00:00.000Z",
      "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752304954/contest_banner/oranges.jpg",
      "bannerPublicId": "contest_banner/oranges",
      "startsAt": "2025-07-20T09:00:00.000Z",
      "endsAt": "2025-07-25T09:00:00.000Z",
      "acceptingRegistrations": true,
      "acceptingVotes": true,
      "requireApproval": false,
      "numberOfWinners": 2,
      "winners": [],
      "status": "draft",
      "createdAt": "2025-07-13T22:50:39.351Z",
      "updatedAt": "2025-07-13T22:59:11.975Z",
      "slug": "face-of-nacos-2025",
      "__v": 0
    },
    {
      "_id": "68720d3b2287c7bc523f1bf4",
      "clientID": "686f4cc83b38daad2b0010a7",
      "title": "third orange test contest",
      "description": "third test description",
      "votePrice": 200,
      "registrationFee": 2000,
      "registrationStartDate": "2025-07-10T09:00:00.000Z",
      "registrationCloseDate": "2025-07-15T09:00:00.000Z",
      "votingStartDate": "2025-07-15T09:00:00.000Z",
      "votingCloseDate": "2025-07-20T09:00:00.000Z",
      "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752304954/contest_banner/oranges.jpg",
      "bannerPublicId": "contest_banner/oranges",
      "startsAt": "2025-07-20T09:00:00.000Z",
      "endsAt": "2025-07-25T09:00:00.000Z",
      "acceptingRegistrations": true,
      "acceptingVotes": true,
      "requireApproval": false,
      "numberOfWinners": 2,
      "winners": [],
      "status": "draft",
      "createdAt": "2025-07-12T07:22:35.680Z",
      "updatedAt": "2025-07-12T07:22:35.680Z",
      "slug": "third-orange-test-contest",
      "__v": 0
    },
    {
      "_id": "6871f9a2cd7701c4424723b2",
      "clientID": "686f4cc83b38daad2b0010a7",
      "title": "second orange test contest",
      "description": "second test description",
      "votePrice": 200,
      "registrationFee": 3000,
      "registrationStartDate": "2025-07-10T09:00:00.000Z",
      "registrationCloseDate": "2025-07-15T09:00:00.000Z",
      "votingStartDate": "2025-07-15T09:00:00.000Z",
      "votingCloseDate": "2025-07-20T09:00:00.000Z",
      "bannerUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752277827/contest_banner/oranges.jpg",
      "bannerPublicId": "contest_banner/oranges",
      "startsAt": "2025-07-20T09:00:00.000Z",
      "endsAt": "2025-07-25T09:00:00.000Z",
      "acceptingRegistrations": true,
      "acceptingVotes": true,
      "requireApproval": false,
      "numberOfWinners": 2,
      "winners": [],
      "status": "draft",
      "createdAt": "2025-07-12T05:58:58.169Z",
      "updatedAt": "2025-07-12T05:58:58.169Z",
      "slug": "second-orange-test-contest",
      "__v": 0
    }
  ]
}




POST
create contestant
https://kontestica-backend-api.onrender.com/api/contestants/
Register a new contestant into a contest with optional profile picture upload.

AUTHORIZATION
Bearer Token
Token
<token>

HEADERS
Accept
multipart/form-data

Body
formdata
name
frog toast

stageName
frog toast

email
<EMAIL>

phone
09100000076

contestID
68720d3b2287c7bc523f1bf4

bio
software engineer

paymentReference
T658984703746647

contestantphoto
Example Request
create contestant
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contestants/' \
--header 'Accept: multipart/form-data' \
--form 'name="tim cook"' \
--form 'stageName="tim cook"' \
--form 'email="<EMAIL>"' \
--form 'phone="09100000000"' \
--form 'contestID="68720d3b2287c7bc523f1bf4"' \
--form 'bio="software engineer"' \
--form 'paymentReference="T658984703746647"' \
--form 'contestantphoto=@"/C:/Users/<USER>/Downloads/user.png"'
201 Created
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestant registered",
  "contestant": {
    "contestID": "68720d3b2287c7bc523f1bf4",
    "name": "tim cook",
    "stageName": "tim cook",
    "email": "<EMAIL>",
    "phone": "09100000000",
    "bio": "software engineer",
    "profilePictureUrl": "",
    "profilePicturePublicId": "",
    "paymentReference": "T658984703746647",
    "status": "pending",
    "voteCount": 0,
    "_id": "68743e65f4cad43c3c951674",
    "createdAt": "2025-07-13T23:16:53.247Z",
    "updatedAt": "2025-07-13T23:16:53.247Z",
    "slug": "tim-cook",
    "__v": 0
  }
}
PUT
update contestant
https://kontestica-backend-api.onrender.com/api/contestants/68743e65f4cad43c3c951674
Update a contestant’s information.

AUTHORIZATION
Bearer Token
Token
<token>

HEADERS
Accept
multipart/form-data

Body
formdata
name
tim cook updated

stageName
tim cook

email
<EMAIL>

phone
09134560000

contestID
6871f9a2cd7701c4424723b2

bio
software engineer

paymentReference
T658984703746647

contestantphoto
Example Request
update contestant
View More
curl
curl --location --request PUT 'https://kontestica-backend-api.onrender.com/api/contestants/68743e65f4cad43c3c951674' \
--header 'Accept: multipart/form-data' \
--form 'name="tim cook updated"'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestant updated",
  "contestant": {
    "_id": "68743e65f4cad43c3c951674",
    "contestID": "68720d3b2287c7bc523f1bf4",
    "name": "tim cook updated",
    "stageName": "tim cook",
    "email": "<EMAIL>",
    "phone": "09100000000",
    "bio": "software engineer",
    "profilePictureUrl": "",
    "profilePicturePublicId": "",
    "paymentReference": "T658984703746647",
    "status": "pending",
    "voteCount": 0,
    "createdAt": "2025-07-13T23:16:53.247Z",
    "updatedAt": "2025-07-13T23:28:10.755Z",
    "slug": "tim-cook-updated",
    "__v": 0
  }
}

PUT
approve contestant
https://kontestica-backend-api.onrender.com/api/contestants/68743e65f4cad43c3c951674
Approve or reject a contestant (Client-only access)

AUTHORIZATION
Bearer Token
Token
<token>

PARAMS
status
approved

description
test description

registrationFee
5000

registrationStartDate
Body
urlencoded
status
approved

Example Request
approve contestant
View More
curl
curl --location --request PUT 'https://kontestica-backend-api.onrender.com/api/contestants/68743e65f4cad43c3c951674' \
--data-urlencode 'status=approved'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestant updated",
  "contestant": {
    "_id": "68743e65f4cad43c3c951674",
    "contestID": "68720d3b2287c7bc523f1bf4",
    "name": "tim cook updated",
    "stageName": "tim cook",
    "email": "<EMAIL>",
    "phone": "09100000000",
    "bio": "software engineer",
    "profilePictureUrl": "",
    "profilePicturePublicId": "",
    "paymentReference": "T658984703746647",
    "status": "approved",
    "voteCount": 0,
    "createdAt": "2025-07-13T23:16:53.247Z",
    "updatedAt": "2025-07-13T23:30:48.514Z",
    "slug": "tim-cook",
    "__v": 0
  }
}
GET
Get contestants by Contest ID
https://kontestica-backend-api.onrender.com/api/contestants/?contestID=6874383ff4cad43c3c951664
Fetch contestants for a specific contest by ID.

AUTHORIZATION
Bearer Token
Token
<token>

PARAMS
contestID
6874383ff4cad43c3c951664

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get contestants by Contest ID
View More
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contestants/?contestID=6874383ff4cad43c3c951664'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestants fetched successfully",
  "contestants": [
    {
      "_id": "68743e65f4cad43c3c951674",
      "contestID": {
        "_id": "68720d3b2287c7bc523f1bf4",
        "title": "third orange test contest"
      },
      "name": "tim cook updated",
      "stageName": "tim cook",
      "email": "<EMAIL>",
      "phone": "09100000000",
      "bio": "software engineer",
      "profilePictureUrl": "",
      "profilePicturePublicId": "",
      "paymentReference": "T658984703746647",
      "status": "approved",
      "voteCount": 0,
      "createdAt": "2025-07-13T23:16:53.247Z",
      "updatedAt": "2025-07-13T23:30:48.514Z",
      "slug": "tim-cook",
      "__v": 0
    },
    {
      "_id": "687230376d7df71d00551cd5",
      "contestID": {
        "_id": "6871f9a2cd7701c4424723b2",
        "title": "second orange test contest"
      },
      "name": "dev test",
      "stageName": "amzing dev",
      "email": "<EMAIL>",
      "phone": "0177688855",
      "bio": "test dev",
      "profilePictureUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752300331/contestant_photo/user.png",
      "profilePicturePublicId": "contestant_photo/user",
      "paymentReference": "T464678312530545",
      "status": "pending",
      "voteCount": 0,
      "createdAt": "2025-07-12T09:51:51.601Z",
      "updatedAt": "2025-07-12T09:51:51.601Z",
      "slug": "amzing-dev",
      "__v": 0
    }
  ],
  "pagination": {
    "total": 2,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}

GET
get all contestants with pagination
https://kontestica-backend-api.onrender.com/api/contestants/?limit=2
Same as above, with support for pagination and status filtering.

AUTHORIZATION
Bearer Token
Token
<token>

PARAMS
limit
2

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
get all contestants with pagination
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contestants/?limit=2'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestants fetched successfully",
  "contestants": [
    {
      "_id": "68743e65f4cad43c3c951674",
      "contestID": {
        "_id": "68720d3b2287c7bc523f1bf4",
        "title": "third orange test contest"
      },
      "name": "tim cook updated",
      "stageName": "tim cook",
      "email": "<EMAIL>",
      "phone": "09100000000",
      "bio": "software engineer",
      "profilePictureUrl": "",
      "profilePicturePublicId": "",
      "paymentReference": "T658984703746647",
      "status": "approved",
      "voteCount": 0,
      "createdAt": "2025-07-13T23:16:53.247Z",
      "updatedAt": "2025-07-13T23:30:48.514Z",
      "slug": "tim-cook",
      "__v": 0
    },
    {
      "_id": "687230376d7df71d00551cd5",
      "contestID": {
        "_id": "6871f9a2cd7701c4424723b2",
        "title": "second orange test contest"
      },
      "name": "dev test",
      "stageName": "amzing dev",
      "email": "<EMAIL>",
      "phone": "0177688855",
      "bio": "test dev",
      "profilePictureUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752300331/contestant_photo/user.png",
      "profilePicturePublicId": "contestant_photo/user",
      "paymentReference": "T464678312530545",
      "status": "pending",
      "voteCount": 0,
      "createdAt": "2025-07-12T09:51:51.601Z",
      "updatedAt": "2025-07-12T09:51:51.601Z",
      "slug": "amzing-dev",
      "__v": 0
    }
  ],
  "pagination": {
    "total": 2,
    "page": 1,
    "limit": 2,
    "totalPages": 1
  }
}
GET
Get All Clients contestants
https://kontestica-backend-api.onrender.com/api/contestants
Get all contestants created by the authenticated client.

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get All Clients contestants
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contestants'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestants fetched successfully",
  "contestants": [
    {
      "_id": "68743e65f4cad43c3c951674",
      "contestID": {
        "_id": "68720d3b2287c7bc523f1bf4",
        "title": "third orange test contest"
      },
      "name": "tim cook updated",
      "stageName": "tim cook",
      "email": "<EMAIL>",
      "phone": "09100000000",
      "bio": "software engineer",
      "profilePictureUrl": "",
      "profilePicturePublicId": "",
      "paymentReference": "T658984703746647",
      "status": "approved",
      "voteCount": 0,
      "createdAt": "2025-07-13T23:16:53.247Z",
      "updatedAt": "2025-07-13T23:30:48.514Z",
      "slug": "tim-cook",
      "__v": 0
    },
    {
      "_id": "687230376d7df71d00551cd5",
      "contestID": {
        "_id": "6871f9a2cd7701c4424723b2",
        "title": "second orange test contest"
      },
      "name": "dev test",
      "stageName": "amzing dev",
      "email": "<EMAIL>",
      "phone": "0177688855",
      "bio": "test dev",
      "profilePictureUrl": "https://res.cloudinary.com/dqlub5azr/image/upload/v1752300331/contestant_photo/user.png",
      "profilePicturePublicId": "contestant_photo/user",
      "paymentReference": "T464678312530545",
      "status": "pending",
      "voteCount": 0,
      "createdAt": "2025-07-12T09:51:51.601Z",
      "updatedAt": "2025-07-12T09:51:51.601Z",
      "slug": "amzing-dev",
      "__v": 0
    }
  ],
  "pagination": {
    "total": 2,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}

GET
Get Contestant By ID
https://kontestica-backend-api.onrender.com/api/contestants/68743e65f4cad43c3c951674
Retrieve a specific contestant by ID.

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get Contestant By ID
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contestants/68743e65f4cad43c3c951674'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestant fetched successfully",
  "contestant": {
    "_id": "68743e65f4cad43c3c951674",
    "contestID": {
      "_id": "68720d3b2287c7bc523f1bf4",
      "title": "third orange test contest"
    },
    "name": "tim cook updated",
    "stageName": "tim cook",
    "email": "<EMAIL>",
    "phone": "09100000000",
    "bio": "software engineer",
    "profilePictureUrl": "",
    "profilePicturePublicId": "",
    "paymentReference": "T658984703746647",
    "status": "approved",
    "voteCount": 0,
    "createdAt": "2025-07-13T23:16:53.247Z",
    "updatedAt": "2025-07-13T23:30:48.514Z",
    "slug": "tim-cook",
    "__v": 0
  }
}
GET
Get Contestant By Stage name
https://kontestica-backend-api.onrender.com/api/contestants/stage-name/tim cook
Search for a contestant using their stage name

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Get Contestant By Stage name
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/contestants/stage-name/tim cook'
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "success": true,
  "message": "Contestants fetched successfully",
  "contestants": [
    {
      "_id": "68743e65f4cad43c3c951674",
      "contestID": "68720d3b2287c7bc523f1bf4",
      "name": "tim cook updated",
      "stageName": "tim cook",
      "email": "<EMAIL>",
      "phone": "09100000000",
      "bio": "software engineer",
      "profilePictureUrl": "",
      "profilePicturePublicId": "",
      "paymentReference": "T658984703746647",
      "status": "approved",
      "voteCount": 0,
      "createdAt": "2025-07-13T23:16:53.247Z",
      "updatedAt": "2025-07-13T23:30:48.514Z",
      "slug": "tim-cook",
      "__v": 0
    }
  ]
}
DELETE
Delete Contestant
https://kontestica-backend-api.onrender.com/api/contestants/6874445af4cad43c3c95169b
Remove a contestant and delete their profile image from Cloudinary.

AUTHORIZATION
Bearer Token
Token
<token>

Body
formdata
title
second test contest updated

description
test description

votePrice
10000

registrationFee
5000

registrationStartDate
2025-07-10T09:00:00Z

registrationCloseDate
2025-07-15T09:00:00Z

votingStartDate
2025-07-15T09:00:00Z

votingCloseDate
2025-07-20T09:00:00Z

startsAt
2025-07-20T09:00:00Z

endsAt
2025-07-25T09:00:00Z

requireApproval
true

numberOfWinners
3

banner
logo
Example Request
Delete Contest
View More
curl
curl --location --request DELETE 'https://kontestica-backend-api.onrender.com/api/contestants/6874445af4cad43c3c95169b' \
--form 'title="second test contest updated"' \
--form 'description="test description"' \
--form 'votePrice="10000"' \
--form 'banner=@"/C:/Users/<USER>/Downloads/oranges.jpeg"' \
--form 'logo=@"/C:/Users/<USER>/Downloads/user.png"' \
--form 'logo=@"/C:/Users/<USER>/Downloads/apples.jpeg"'
200 OK
Example Response
Body
Headers (27)
json
{
  "success": true,
  "message": "Contestant deleted successfully"
}
VOTE CONTESTANTS
POST
Vote Contestant
https://kontestica-backend-api.onrender.com/api/vote-contestants/68743e65f4cad43c3c951674
Cast votes for a contestant. Requires Paystack payment reference and verifies vote count based on payment

AUTHORIZATION
Bearer Token
Token
<token>

HEADERS
Accept
multipart/form-data

Body
urlencoded
paymentReference
T447057875229814

email
<EMAIL>

Example Request
Vote Contestant
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/vote-contestants/68743e65f4cad43c3c951674' \
--header 'Accept: multipart/form-data' \
--data-urlencode 'paymentReference=T447057875229814' \
--data-urlencode 'email=<EMAIL>' \
--data-urlencode '='
400 Bad Request
Example Response
Body
Headers (27)
json
{
  "success": false,
  "message": "Payment reference already used for voting"
}
POST
https://kontestica-backend-api.onrender.com/api/vote-contestants/68743e65f4cad43c3c951674
https://kontestica-backend-api.onrender.com/api/vote-contestants/68743e65f4cad43c3c951674
AUTHORIZATION
Bearer Token
Token
<token>

HEADERS
Accept
multipart/form-data

Body
urlencoded
paymentReference
T233278094319522

email
<EMAIL>

Example Request
https://kontestica-backend-api.onrender.com/api/vote-contestants/68743e65f4cad43c3c951674
curl
curl --location 'https://kontestica-backend-api.onrender.com/api/vote-contestants/68743e65f4cad43c3c951674' \
--header 'Accept: multipart/form-data' \
--data-urlencode 'paymentReference=T233278094319522' \
--data-urlencode 'email=<EMAIL>' \
--data-urlencode '='
200 OK
Example Response
Body
Headers (27)
json
{
  "success": true,
  "message": "20 vote(s) successfully counted",
  "votes": 23
}