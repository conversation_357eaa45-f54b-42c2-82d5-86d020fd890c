"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  Users,
  Trophy,
  DollarSign,
  Heart,
  Share2,
  ArrowLeft,
  Star,
  Vote,
  UserPlus,
  Eye,
  MapPin,
  Award,
  Target
} from "lucide-react";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { ContestsService } from "@/lib/api/contests";
import { ContestantsService } from "@/lib/api/contestants";
import VotingModal from "@/components/voting/VotingModal";
import ContestantRegistrationModal from "@/components/contests/ContestantRegistrationModal";

interface Contest {
  _id: string;
  title: string;
  description: string;
  bannerUrl: string;
  votePrice: number;
  registrationFee: number;
  registrationStartDate: string;
  registrationCloseDate: string;
  votingStartDate: string;
  votingCloseDate: string;
  startsAt: string;
  endsAt: string;
  acceptingRegistrations: boolean;
  acceptingVotes: boolean;
  requireApproval: boolean;
  numberOfWinners: number;
  status: string;
  slug: string;
}

interface Contestant {
  _id: string;
  name: string;
  stageName: string;
  bio: string;
  profilePictureUrl: string;
  voteCount: number;
  status: string;
  slug: string;
}

export default function ContestDetailPage() {
  const params = useParams();
  const router = useRouter();
  const contestId = params.id as string;

  const [contest, setContest] = useState<Contest | null>(null);
  const [contestants, setContestants] = useState<Contestant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showVotingModal, setShowVotingModal] = useState(false);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [selectedContestant, setSelectedContestant] = useState<Contestant | null>(null);

  // Fetch contest details and contestants
  useEffect(() => {
    const fetchContestData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch contest details
        const contest = await ContestsService.getContest(contestId);
        setContest(contest);

        // Fetch contestants for this contest
        const contestantsResponse = await ContestantsService.getContestantsByContest(contestId);

        if (contestantsResponse.success && contestantsResponse.contestants) {
          setContestants(contestantsResponse.contestants);
        }
      } catch (err: any) {
        console.error("Error fetching contest data:", err);
        setError("Failed to load contest details");
      } finally {
        setLoading(false);
      }
    };

    if (contestId) {
      fetchContestData();
    }
  }, [contestId]);

  const handleVoteClick = (contestant: Contestant) => {
    setSelectedContestant(contestant);
    setShowVotingModal(true);
  };

  const handleRegisterClick = () => {
    setShowRegistrationModal(true);
  };

  const isRegistrationOpen = () => {
    if (!contest) return false;
    const now = new Date();
    const regStart = new Date(contest.registrationStartDate);
    const regEnd = new Date(contest.registrationCloseDate);
    return now >= regStart && now <= regEnd && contest.acceptingRegistrations;
  };

  const isVotingOpen = () => {
    if (!contest) return false;
    const now = new Date();
    const voteStart = new Date(contest.votingStartDate);
    const voteEnd = new Date(contest.votingCloseDate);
    return now >= voteStart && now <= voteEnd && contest.acceptingVotes;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !contest) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              {error || "Contest not found"}
            </h1>
            <Button onClick={() => router.push('/contests')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Contests
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <div className="relative h-96 overflow-hidden">
        <img
          src={contest.bannerUrl || "/api/placeholder/1200/400"}
          alt={contest.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-50" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white max-w-4xl mx-auto px-4">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-6xl font-bold mb-4"
            >
              {contest.title}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl md:text-2xl text-gray-200"
            >
              {contest.description}
            </motion.p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => router.push('/contests')}
          className="mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Contests
        </Button>

        {/* Contest Info */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2">
            <Card className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Contest Details
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {contest.description}
              </p>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg mx-auto mb-2">
                    <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Contestants</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {contestants.length}
                  </p>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg mx-auto mb-2">
                    <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Vote Price</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    ₦{contest.votePrice}
                  </p>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg mx-auto mb-2">
                    <Trophy className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Winners</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {contest.numberOfWinners}
                  </p>
                </div>

                <div className="text-center">
                  <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg mx-auto mb-2">
                    <UserPlus className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Entry Fee</p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    ₦{contest.registrationFee}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          <div>
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Contest Timeline
              </h3>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Registration</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {new Date(contest.registrationStartDate).toLocaleDateString()} - {new Date(contest.registrationCloseDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Vote className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Voting Period</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {new Date(contest.votingStartDate).toLocaleDateString()} - {new Date(contest.votingCloseDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Trophy className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Contest Period</p>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {new Date(contest.startsAt).toLocaleDateString()} - {new Date(contest.endsAt).toLocaleDateString()}
                    </p>
                  </div>

        {/* Contestants Grid */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Contestants ({contestants.length})
            </h2>
            {isVotingOpen() && (
              <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Voting Open</span>
              </div>
            )}
          </div>

          {contestants.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No contestants yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Be the first to register for this contest!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {contestants.map((contestant) => (
                <motion.div
                  key={contestant._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="aspect-w-3 aspect-h-4">
                    <img
                      src={contestant.profilePictureUrl || "/api/placeholder/300/400"}
                      alt={contestant.name}
                      className="w-full h-64 object-cover"
                    />
                  </div>

                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                      {contestant.stageName}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      {contestant.name}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                      {contestant.bio}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Heart className="h-4 w-4 text-red-500" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {contestant.voteCount} votes
                        </span>
                      </div>

                      {isVotingOpen() && (
                        <Button
                          size="sm"
                          variant="gradient"
                          onClick={() => handleVoteClick(contestant)}
                        >
                          <Vote className="h-4 w-4 mr-1" />
                          Vote
                        </Button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </Card>
      </div>

      {/* Voting Modal */}
      {selectedContestant && (
        <VotingModal
          isOpen={showVotingModal}
          onClose={() => {
            setShowVotingModal(false);
            setSelectedContestant(null);
          }}
          contestant={selectedContestant}
          contest={{
            _id: contest._id,
            title: contest.title,
            votePrice: contest.votePrice
          }}
          onSuccess={(voteData) => {
            console.log('Vote successful:', voteData);
            // Refresh contestants to update vote count
            const fetchContestants = async () => {
              const response = await ContestantsService.getContestantsByContest(contestId);
              if (response.success && response.contestants) {
                setContestants(response.contestants);
              }
            };
            fetchContestants();
          }}
        />
      )}

      {/* Registration Modal */}
      <ContestantRegistrationModal
        isOpen={showRegistrationModal}
        onClose={() => setShowRegistrationModal(false)}
        contest={{
          _id: contest._id,
          title: contest.title,
          registrationFee: contest.registrationFee,
          requireApproval: contest.requireApproval
        }}
        onSuccess={(registrationData) => {
          console.log('Registration successful:', registrationData);
          // Refresh contestants list
          const fetchContestants = async () => {
            const response = await ContestantsService.getContestantsByContest(contestId);
            if (response.success && response.contestants) {
              setContestants(response.contestants);
            }
          };
          fetchContestants();
        }}
      />
    </div>
  );
}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-6 space-y-3">
                {isRegistrationOpen() && (
                  <Button
                    variant="gradient"
                    fullWidth
                    onClick={handleRegisterClick}
                    glow={true}
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    Register as Contestant
                  </Button>
                )}

                <Button variant="outline" fullWidth>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Contest
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* Contestants Grid */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Contestants ({contestants.length})
            </h2>
            {isVotingOpen() && (
              <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">Voting Open</span>
              </div>
            )}
          </div>

          {contestants.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No contestants yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Be the first to register for this contest!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {contestants.map((contestant) => (
                <motion.div
                  key={contestant._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                >
                  <div className="aspect-w-3 aspect-h-4">
                    <img
                      src={contestant.profilePictureUrl || "/api/placeholder/300/400"}
                      alt={contestant.name}
                      className="w-full h-64 object-cover"
                    />
                  </div>

                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                      {contestant.stageName}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      {contestant.name}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                      {contestant.bio}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Heart className="h-4 w-4 text-red-500" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {contestant.voteCount} votes
                        </span>
                      </div>

                      {isVotingOpen() && (
                        <Button
                          size="sm"
                          variant="gradient"
                          onClick={() => handleVoteClick(contestant)}
                        >
                          <Vote className="h-4 w-4 mr-1" />
                          Vote
                        </Button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}


  status: "active",
  contestants: 156,
  votes: 1234,
  prize: "₦2,000,000",
  deadline: "2024-12-31",
  startDate: "2024-11-01",
  image: "/api/placeholder/800/400",
  tags: ["Beauty", "Talent", "Nigeria"],
  featured: true,
  entryFee: "₦50,000",
  ageLimit: "18-26 years",
  requirements: [
    "Must be a Nigerian citizen by birth",
    "Single and never been married",
    "No children",
    "Minimum height of 5'6\" (168cm)",
    "University graduate or currently enrolled",
    "Good moral character and clean background"
  ],
  rules: [
    "Contestants must be available for the full duration of the competition",
    "Professional conduct is required at all times",
    "No cosmetic surgery or permanent body modifications",
    "Must be willing to relocate if crowned winner",
    "Voting is open to the public and closes with the contest deadline"
  ],
  criteria: [
    "Beauty and Poise (25%)",
    "Intelligence and Communication (25%)",
    "Talent and Performance (20%)",
    "Cultural Knowledge (15%)",
    "Social Impact and Advocacy (15%)"
  ],
  contestants_list: [
    {
      id: "1",
      name: "Adunni Adeola",
      age: 23,
      location: "Lagos State",
      bio: "A passionate advocate for women's education and environmental sustainability. Currently pursuing a Master's degree in International Relations at the University of Lagos.",
      votes: 234,
      image: "/api/placeholder/300/400",
      registeredAt: "2024-11-15",
      featured: true,
      socialMedia: {
        instagram: "@adunni_adeola",
        twitter: "@adunni_speaks"
      }
    },
    {
      id: "2",
      name: "Chioma Okwu",
      age: 22,
      location: "Anambra State",
      bio: "A talented dancer and model with a passion for promoting Nigerian culture through arts. Graduate of Theatre Arts from Nnamdi Azikiwe University.",
      votes: 189,
      image: "/api/placeholder/300/400",
      registeredAt: "2024-11-12",
      featured: false,
      socialMedia: {
        instagram: "@chioma_okwu",
        facebook: "Chioma Okwu Official"
      }
    },
    {
      id: "3",
      name: "Fatima Abdullahi",
      age: 24,
      location: "Kano State",
      bio: "A medical student and youth advocate focused on healthcare accessibility in rural communities. Speaks four languages fluently.",
      votes: 156,
      image: "/api/placeholder/300/400",
      registeredAt: "2024-11-10",
      featured: true,
      socialMedia: {
        instagram: "@fatima_abdullahi",
        twitter: "@fatima_health"
      }
    },
    {
      id: "4",
      name: "Blessing Okafor",
      age: 21,
      location: "Rivers State",
      bio: "An entrepreneur and tech enthusiast who founded a successful e-commerce platform for local artisans. Computer Science graduate.",
      votes: 143,
      image: "/api/placeholder/300/400",
      registeredAt: "2024-11-08",
      featured: false,
      socialMedia: {
        instagram: "@blessing_okafor",
        twitter: "@blessing_tech"
      }
    },
    {
      id: "5",
      name: "Aisha Mohammed",
      age: 25,
      location: "Kaduna State",
      bio: "A journalist and human rights activist dedicated to promoting peace and unity in Nigeria. Award-winning writer and public speaker.",
      votes: 198,
      image: "/api/placeholder/300/400",
      registeredAt: "2024-11-05",
      featured: false,
      socialMedia: {
        instagram: "@aisha_mohammed",
        twitter: "@aisha_writes"
      }
    },
    {
      id: "6",
      name: "Nneka Okonkwo",
      age: 26,
      location: "Enugu State",
      bio: "A professional model and fitness enthusiast who promotes healthy living and body positivity. Certified nutritionist and wellness coach.",
      votes: 167,
      image: "/api/placeholder/300/400",
      registeredAt: "2024-11-03",
      featured: false,
      socialMedia: {
        instagram: "@nneka_fitness",
        facebook: "Nneka Wellness"
      }
    }
  ]
};

// Helper functions
function getStatusColor(status: string) {
  switch (status) {
    case "active":
      return "text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30";
    case "upcoming":
      return "text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/30";
    case "voting":
      return "text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/30";
    case "completed":
      return "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30";
    default:
      return "text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30";
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric"
  });
}

function getTimeRemaining(deadline: string) {
  const now = new Date();
  const end = new Date(deadline);
  const diff = end.getTime() - now.getTime();
  
  if (diff <= 0) return "Contest ended";
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  
  if (days > 0) {
    return `${days} day${days !== 1 ? 's' : ''} remaining`;
  } else {
    return `${hours} hour${hours !== 1 ? 's' : ''} remaining`;
  }
}

export default function ContestDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [contest, setContest] = useState<Contest | null>(null);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<"votes" | "recent" | "featured">("votes");
  const [showRegistration, setShowRegistration] = useState(false);
  const [showVoting, setShowVoting] = useState(false);
  const [selectedContestant, setSelectedContestant] = useState<Contestant | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false); // Replace with actual auth state

  useEffect(() => {
    const fetchContest = async () => {
      try {
        setLoading(true);

        // Fetch contest by ID
        const apiContest = await ContestsService.getContest(params.id as string);

        // Fetch contestants for this contest
        const contestantsResponse = await ContestantsService.getContestantsByContest(apiContest._id);

        // Transform API data to match our interface
        const transformedContest: Contest = {
          id: apiContest._id,
          title: apiContest.title,
            description: apiContest.description,
            organizer: "Contest Organizer", // API doesn't provide organizer name directly
            category: "General", // You might want to add category to API
            status: apiContest.status === 'draft' ? 'upcoming' : 'active',
            contestants: contestantsResponse.success ? contestantsResponse.contestants?.length || 0 : 0,
            votes: 0, // You'll need to get this from voting API
            prize: `₦${apiContest.registrationFee?.toLocaleString() || '0'}`,
            deadline: apiContest.endsAt,
            startDate: apiContest.startsAt,
            image: apiContest.bannerUrl || "/api/placeholder/400/250",
            tags: [],
            featured: false,
            rules: ["Follow contest guidelines", "Be respectful to other participants"],
            criteria: ["Creativity", "Originality", "Presentation"],
            entryFee: `₦${apiContest.registrationFee?.toLocaleString() || '0'}`,
            requirements: ["Must be 18 years or older", "Original content only"],
            contestants_list: contestantsResponse.success ?
              contestantsResponse.contestants?.map(contestant => ({
                id: contestant._id,
                name: contestant.name,
                stageName: contestant.stageName,
                image: contestant.profilePictureUrl || "/api/placeholder/150/150",
                votes: contestant.voteCount || 0,
                bio: contestant.bio,
                featured: false,
                socialMedia: {
                  instagram: "",
                  twitter: "",
                  facebook: ""
                }
              })) || [] : []
          };

          setContest(transformedContest);
        } else {
          // Fallback to mock data if API fails
          setContest(mockContest);
        }
      } catch (error) {
        console.error("Error fetching contest:", error);
        // Fallback to mock data
        setContest(mockContest);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchContest();
    }
  }, [params.id]);

  if (loading) {
    return (
      <MainContainer>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
              <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </MainContainer>
    );
  }

  if (!contest) {
    return (
      <MainContainer>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Contest Not Found
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The contest you're looking for doesn't exist or has been removed.
            </p>
            <Button
              variant="outline"
              leftIcon={<ArrowLeft className="w-4 h-4" />}
              onClick={() => router.push("/contests")}
            >
              Back to Contests
            </Button>
          </Card>
        </div>
      </MainContainer>
    );
  }

  const sortedContestants = [...contest.contestants_list].sort((a, b) => {
    switch (sortBy) {
      case "votes":
        return b.votes - a.votes;
      case "recent":
        return new Date(b.registeredAt).getTime() - new Date(a.registeredAt).getTime();
      case "featured":
        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
      default:
        return 0;
    }
  });

  return (
    <MainContainer>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Button
          variant="ghost"
          leftIcon={<ArrowLeft className="w-4 h-4" />}
          onClick={() => router.push("/contests")}
          className="mb-6"
        >
          Back to Contests
        </Button>

        {/* Contest Header */}
        <Card className="mb-8">
          <div className="relative">
            {/* Contest Image */}
            <div className="aspect-[2/1] bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-t-xl overflow-hidden">
              <div className="w-full h-full flex items-center justify-center">
                <Trophy className="w-24 h-24 text-gray-400" />
              </div>
            </div>
            
            {/* Featured Badge */}
            {contest.featured && (
              <div className="absolute top-4 left-4">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                  <Star className="w-4 h-4 mr-1" />
                  Featured Contest
                </span>
              </div>
            )}
            
            {/* Status Badge */}
            <div className="absolute top-4 right-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium capitalize ${getStatusColor(contest.status)}`}>
                {contest.status}
              </span>
            </div>
          </div>
          
          <div className="p-8">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
              <div className="flex-1">
                <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                  {contest.title}
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-400 mb-4">
                  Organized by <span className="font-semibold">{contest.organizer}</span>
                </p>
                <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                  {contest.description}
                </p>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {contest.tags.map(tag => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                
                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  <Button
                    variant="gradient"
                    leftIcon={<Users className="w-4 h-4" />}
                    glow={true}
                    onClick={() => setShowRegistration(true)}
                    motionProps={{
                      whileHover: { scale: 1.05, y: -2 },
                      whileTap: { scale: 0.95 }
                    }}
                  >
                    Register for Contest
                  </Button>
                  <Button
                    variant="primary"
                    leftIcon={<Heart className="w-4 h-4" />}
                    motionProps={{
                      whileHover: { scale: 1.05, y: -2 },
                      whileTap: { scale: 0.95 }
                    }}
                  >
                    Vote Now
                  </Button>
                  <Button
                    variant="outline"
                    leftIcon={<Share2 className="w-4 h-4" />}
                    motionProps={{
                      whileHover: { scale: 1.02 },
                      whileTap: { scale: 0.98 }
                    }}
                  >
                    Share Contest
                  </Button>
                  <Button
                    variant="ghost"
                    leftIcon={<ExternalLink className="w-4 h-4" />}
                    motionProps={{
                      whileHover: { scale: 1.02 },
                      whileTap: { scale: 0.98 }
                    }}
                  >
                    Visit Organizer
                  </Button>
                </div>
              </div>
              
              {/* Contest Stats */}
              <div className="lg:w-80">
                <Card variant="elevated" className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Contest Details
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600 dark:text-gray-400">
                        <DollarSign className="w-5 h-5 mr-2" />
                        Prize Pool
                      </div>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {contest.prize}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600 dark:text-gray-400">
                        <Users className="w-5 h-5 mr-2" />
                        Contestants
                      </div>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {contest.contestants}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600 dark:text-gray-400">
                        <Eye className="w-5 h-5 mr-2" />
                        Total Votes
                      </div>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {contest.votes.toLocaleString()}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600 dark:text-gray-400">
                        <Calendar className="w-5 h-5 mr-2" />
                        Started
                      </div>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {formatDate(contest.startDate)}
                      </span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-gray-600 dark:text-gray-400">
                        <Timer className="w-5 h-5 mr-2" />
                        Deadline
                      </div>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">
                        {formatDate(contest.deadline)}
                      </span>
                    </div>
                    
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center text-orange-600 dark:text-orange-400">
                        <Clock className="w-5 h-5 mr-2" />
                        <span className="font-semibold">
                          {getTimeRemaining(contest.deadline)}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </Card>

        {/* Contestants Section - Full Width */}
        <Card>
          <div className="p-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4 sm:mb-0 flex items-center">
                <Trophy className="w-6 h-6 mr-3 text-yellow-500" />
                Contestants ({contest.contestants_list.length})
              </h2>

              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="votes">Most Votes</option>
                  <option value="recent">Most Recent</option>
                  <option value="featured">Featured First</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {sortedContestants.map((contestant, index) => (
                <ContestantCard
                  key={contestant.id}
                  contestant={contestant}
                  index={index}
                  contest={contest}
                  onVoteClick={(contestant) => {
                    setSelectedContestant(contestant);
                    setShowVoting(true);
                  }}
                />
              ))}
            </div>
          </div>
        </Card>

        {/* Registration Modal */}
        {contest && (
          <ContestRegistration
            contest={{
              id: contest.id,
              title: contest.title,
              entryFee: contest.entryFee,
              requirements: contest.requirements,
              deadline: contest.deadline
            }}
            isOpen={showRegistration}
            onClose={() => setShowRegistration(false)}
            onSuccess={(registrationData) => {
              console.log('Registration successful:', registrationData);
              setShowRegistration(false);
              // Show success message
              alert('Registration successful! You have been registered for the contest.');
            }}
          />
        )}

        {/* Voting Modal */}
        {selectedContestant && contest && (
          <VotingModal
            contestant={selectedContestant}
            contest={{
              id: contest.id,
              title: contest.title,
              votePrice: "100" // Replace with actual vote price from contest
            }}
            isOpen={showVoting}
            onClose={() => {
              setShowVoting(false);
              setSelectedContestant(null);
            }}
            isLoggedIn={isLoggedIn}
            onLoginRequired={() => {
              // Redirect to login page
              router.push('/login');
            }}
            onSuccess={(voteData) => {
              console.log('Vote successful:', voteData);
              setShowVoting(false);
              setSelectedContestant(null);
              // Update contestant votes in real app
            }}
          />
        )}
      </div>
    </MainContainer>
  );
}

// Contestant Card Component
function ContestantCard({
  contestant,
  index,
  contest,
  onVoteClick
}: {
  contestant: Contestant;
  index: number;
  contest: Contest;
  onVoteClick: (contestant: Contestant) => void;
}) {
  const router = useRouter();
  const [hasVoted, setHasVoted] = useState(false);
  const [voteCount, setVoteCount] = useState(contestant.votes);

  const handleVote = () => {
    if (!hasVoted) {
      setVoteCount(prev => prev + 1);
      setHasVoted(true);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.5 }}
      whileHover={{ y: -5 }}
    >
      <Card hover interactive className="h-full overflow-hidden group">
        <div className="relative">
          {/* Contestant Image */}
          <div className="aspect-[3/4] bg-gradient-to-br from-primary-100 via-primary-50 to-secondary-50 dark:from-gray-800 dark:via-gray-700 dark:to-gray-600 rounded-t-xl overflow-hidden relative">
            <div className="w-full h-full flex items-center justify-center">
              <Users className="w-16 h-16 text-primary-400 dark:text-gray-400 group-hover:scale-110 transition-transform duration-300" />
            </div>

            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </div>

          {/* Featured Badge */}
          {contestant.featured && (
            <div className="absolute top-4 left-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg">
                <Star className="w-3 h-3 mr-1 fill-current" />
                Featured
              </span>
            </div>
          )}

          {/* Vote Count Badge */}
          <div className="absolute top-4 right-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 dark:bg-gray-900/90 text-gray-900 dark:text-gray-100 shadow-lg backdrop-blur-sm">
              <Heart className="w-3 h-3 mr-1 text-red-500" />
              {voteCount}
            </span>
          </div>

          {/* Ranking Badge */}
          <div className="absolute bottom-4 left-4">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg">
              #{index + 1}
            </span>
          </div>
        </div>

        <div className="p-6">
          <div className="text-center mb-6">
            <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
              {contestant.name}
            </h4>
            <div className="flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
              <span className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                {contestant.age} years
              </span>
              <span>•</span>
              <span>{contestant.location}</span>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              Joined {formatDate(contestant.registeredAt)}
            </p>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6 line-clamp-3 text-center leading-relaxed">
            {contestant.bio}
          </p>

          {/* Social Media Links */}
          {contestant.socialMedia && (
            <div className="flex justify-center gap-2 mb-6">
              {contestant.socialMedia.instagram && (
                <span className="text-xs bg-gradient-to-r from-pink-500 to-purple-500 text-white px-3 py-1 rounded-full font-medium">
                  {contestant.socialMedia.instagram}
                </span>
              )}
              {contestant.socialMedia.twitter && (
                <span className="text-xs bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-3 py-1 rounded-full font-medium">
                  {contestant.socialMedia.twitter}
                </span>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              fullWidth
              size="md"
              variant="outline"
              leftIcon={<Eye className="w-4 h-4" />}
              onClick={() => router.push(`/contests/${contest.id}/contestants/${contestant.id}`)}
              motionProps={{
                whileHover: { scale: 1.02 },
                whileTap: { scale: 0.98 }
              }}
            >
              View Profile
            </Button>

            <Button
              fullWidth
              size="md"
              variant={hasVoted ? "primary" : "gradient"}
              leftIcon={<Heart className={`w-4 h-4 ${hasVoted ? 'fill-current' : ''}`} />}
              onClick={() => onVoteClick(contestant)}
              disabled={hasVoted}
              glow={!hasVoted}
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 }
              }}
              className="font-semibold"
            >
              {hasVoted ? '✓ Voted!' : 'Vote Now'}
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
