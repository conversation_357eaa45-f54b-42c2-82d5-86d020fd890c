"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Users,
  Trophy,
  DollarSign,
  TrendingUp,
  Building,
  UserCheck,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3
} from "lucide-react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { ContestsService } from "@/lib/api/contests";
import { ContestantsService } from "@/lib/api/contestants";

interface StatCard {
  title: string;
  value: string;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  icon: any;
  color: string;
}

interface RecentActivity {
  id: string;
  type: "vendor" | "contest" | "payment" | "user";
  title: string;
  description: string;
  time: string;
  status: "success" | "warning" | "error" | "info";
}

const statsCards: StatCard[] = [
  {
    title: "Total Vendors",
    value: "1,234",
    change: "+12%",
    changeType: "positive",
    icon: Building,
    color: "text-blue-600 dark:text-blue-400"
  },
  {
    title: "Active Contests",
    value: "89",
    change: "+8%",
    changeType: "positive",
    icon: Trophy,
    color: "text-yellow-600 dark:text-yellow-400"
  },
  {
    title: "Total Revenue",
    value: "$45,678",
    change: "+23%",
    changeType: "positive",
    icon: DollarSign,
    color: "text-green-600 dark:text-green-400"
  },
  {
    title: "Platform Growth",
    value: "15.8%",
    change: "+2.1%",
    changeType: "positive",
    icon: TrendingUp,
    color: "text-purple-600 dark:text-purple-400"
  }
];

const recentActivities: RecentActivity[] = [
  {
    id: "1",
    type: "vendor",
    title: "New Vendor Registration",
    description: "TechCorp submitted vendor application",
    time: "5 minutes ago",
    status: "info"
  },
  {
    id: "2",
    type: "contest",
    title: "Contest Completed",
    description: "Best Innovation Award 2024 has ended",
    time: "1 hour ago",
    status: "success"
  },
  {
    id: "3",
    type: "payment",
    title: "Payment Processed",
    description: "$2,500 payout to VendorXYZ",
    time: "2 hours ago",
    status: "success"
  },
  {
    id: "4",
    type: "vendor",
    title: "Vendor Approved",
    description: "EventMasters account activated",
    time: "3 hours ago",
    status: "success"
  },
  {
    id: "5",
    type: "contest",
    title: "Contest Issue",
    description: "Voting system error in Contest #1234",
    time: "4 hours ago",
    status: "error"
  }
];

const pendingApprovals = [
  { id: "1", name: "TechCorp Solutions", type: "Corporate", submitted: "2 days ago" },
  { id: "2", name: "Creative Studios", type: "Entertainment", submitted: "1 day ago" },
  { id: "3", name: "EduTech Institute", type: "Educational", submitted: "3 hours ago" }
];

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalContests: 0,
    totalContestants: 0,
    totalVotes: 0,
    totalRevenue: 0
  });
  const [recentContests, setRecentContests] = useState<any[]>([]);

  // Fetch platform-wide statistics
  useEffect(() => {
    const fetchPlatformStats = async () => {
      try {
        setLoading(true);

        // Fetch all public contests to get platform statistics
        const contestsResponse = await ContestsService.getPublicContests({
          page: 1,
          limit: 100, // Get more contests for better stats
        });

        if (contestsResponse.success && contestsResponse.contests) {
          const contests = contestsResponse.contests;

          // Calculate platform statistics
          const totalRevenue = contests.reduce((sum, contest) => sum + (contest.registrationFee || 0), 0);

          setStats({
            totalContests: contests.length,
            totalContestants: 0, // Will be calculated from contestants API
            totalVotes: 0, // Will be calculated from voting API
            totalRevenue
          });

          setRecentContests(contests.slice(0, 5));
        }
      } catch (error) {
        console.error('Error fetching platform stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPlatformStats();
  }, []);
  const getActivityIcon = (type: string, status: string) => {
    switch (type) {
      case "vendor":
        return <Building className="h-4 w-4" />;
      case "contest":
        return <Trophy className="h-4 w-4" />;
      case "payment":
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20";
      case "warning":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20";
      case "error":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20";
      default:
        return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20";
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Welcome back! Here's what's happening on your platform.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="primary">
            Generate Report
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: "Total Contests",
            value: loading ? "..." : stats.totalContests.toString(),
            change: "+12%",
            changeType: "positive" as const,
            icon: Trophy,
            color: "text-blue-600 dark:text-blue-400"
          },
          {
            title: "Total Contestants",
            value: loading ? "..." : stats.totalContestants.toString(),
            change: "+8%",
            changeType: "positive" as const,
            icon: Users,
            color: "text-green-600 dark:text-green-400"
          },
          {
            title: "Total Votes",
            value: loading ? "..." : stats.totalVotes.toString(),
            change: "+23%",
            changeType: "positive" as const,
            icon: UserCheck,
            color: "text-purple-600 dark:text-purple-400"
          },
          {
            title: "Platform Revenue",
            value: loading ? "..." : `₦${stats.totalRevenue.toLocaleString()}`,
            change: "+15%",
            changeType: "positive" as const,
            icon: DollarSign,
            color: "text-orange-600 dark:text-orange-400"
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                    {stat.value}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className={`text-sm font-medium ${
                      stat.changeType === "positive" 
                        ? "text-green-600 dark:text-green-400" 
                        : stat.changeType === "negative"
                        ? "text-red-600 dark:text-red-400"
                        : "text-gray-600 dark:text-gray-400"
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-500 ml-1">
                      from last month
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-100 dark:bg-gray-800 ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Recent Activity
              </h2>
              <button className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium">
                View All
              </button>
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${getStatusColor(activity.status)}`}>
                    {getActivityIcon(activity.type, activity.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.title}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Pending Approvals */}
        <div>
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Pending Approvals
              </h2>
              <span className="bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs px-2 py-1 rounded-full">
                {pendingApprovals.length}
              </span>
            </div>
            <div className="space-y-4">
              {pendingApprovals.map((vendor) => (
                <div key={vendor.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                      {vendor.name}
                    </h3>
                    <span className="text-xs text-gray-500 dark:text-gray-500">
                      {vendor.submitted}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                    {vendor.type}
                  </p>
                  <div className="flex space-x-2">
                    <button className="flex-1 px-3 py-1 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-xs rounded hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors">
                      Approve
                    </button>
                    <button className="flex-1 px-3 py-1 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-xs rounded hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors">
                      Reject
                    </button>
                  </div>
                </div>
              ))}
            </div>
            <button className="w-full mt-4 px-4 py-2 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-lg transition-colors text-sm font-medium">
              View All Pending
            </button>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button variant="outline" className="p-4 h-auto justify-start">
            <UserCheck className="h-5 w-5 mr-3 text-blue-600 dark:text-blue-400" />
            <div className="text-left">
              <div className="font-medium text-gray-900 dark:text-white">Review Vendors</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Approve pending applications</div>
            </div>
          </Button>
          <Button variant="outline" className="p-4 h-auto justify-start">
            <BarChart3 className="h-5 w-5 mr-3 text-green-600 dark:text-green-400" />
            <div className="text-left">
              <div className="font-medium text-gray-900 dark:text-white">View Analytics</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Platform performance metrics</div>
            </div>
          </Button>
          <Button variant="outline" className="p-4 h-auto justify-start">
            <DollarSign className="h-5 w-5 mr-3 text-yellow-600 dark:text-yellow-400" />
            <div className="text-left">
              <div className="font-medium text-gray-900 dark:text-white">Manage Payments</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Process payouts and fees</div>
            </div>
          </Button>
          <Button variant="outline" className="p-4 h-auto justify-start">
            <Trophy className="h-5 w-5 mr-3 text-purple-600 dark:text-purple-400" />
            <div className="text-left">
              <div className="font-medium text-gray-900 dark:text-white">Monitor Contests</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Active contest oversight</div>
            </div>
          </Button>
        </div>
      </Card>
    </div>
  );
}
