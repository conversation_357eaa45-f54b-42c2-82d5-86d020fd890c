import { apiClient, ApiResponse } from '../api';

// Voting Types
export interface VoteData {
  paymentReference: string;
  email: string;
}

export interface VoteResponse {
  success: boolean;
  message: string;
  votes: number;
}

export interface VoteRecord {
  _id: string;
  contestantId: string;
  voterEmail: string;
  voteCount: number;
  paymentReference: string;
  amount: number;
  createdAt: string;
  updatedAt: string;
}

export interface VotingStatsResponse {
  success: boolean;
  message: string;
  stats: {
    totalVotes: number;
    totalAmount: number;
    uniqueVoters: number;
    averageVotesPerVoter: number;
    topVoters: Array<{
      email: string;
      totalVotes: number;
      totalAmount: number;
    }>;
  };
}

export interface ContestantVotesResponse {
  success: boolean;
  message: string;
  votes: VoteRecord[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Voting API Service
export class VotingService {
  /**
   * Cast votes for a contestant
   * Requires Paystack payment reference and verifies vote count based on payment
   */
  static async voteContestant(contestantId: string, data: VoteData): Promise<VoteResponse> {
    const response = await apiClient.postForm<VoteResponse>(`/vote-contestants/${contestantId}`, {
      paymentReference: data.paymentReference,
      email: data.email,
    });

    return response;
  }

  /**
   * Get voting history for a specific contestant
   */
  static async getContestantVotes(contestantId: string, params?: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<ContestantVotesResponse> {
    const response = await apiClient.get<ContestantVotesResponse>(`/vote-contestants/${contestantId}/history`, params);
    return response;
  }

  /**
   * Get voting statistics for a contestant
   */
  static async getContestantVotingStats(contestantId: string): Promise<VotingStatsResponse> {
    const response = await apiClient.get<VotingStatsResponse>(`/vote-contestants/${contestantId}/stats`);
    return response;
  }

  /**
   * Get voting statistics for a contest
   */
  static async getContestVotingStats(contestId: string): Promise<VotingStatsResponse> {
    const response = await apiClient.get<VotingStatsResponse>(`/contests/${contestId}/voting-stats`);
    return response;
  }

  /**
   * Get leaderboard for a contest
   */
  static async getContestLeaderboard(contestId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<ApiResponse> {
    const response = await apiClient.get(`/contests/${contestId}/leaderboard`, params);
    return response;
  }

  /**
   * Verify if a payment reference has been used for voting
   */
  static async verifyPaymentReference(paymentReference: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/vote-contestants/verify-payment/${paymentReference}`);
    return response;
  }

  /**
   * Get voting history for a specific voter (by email)
   */
  static async getVoterHistory(email: string, params?: {
    page?: number;
    limit?: number;
    contestId?: string;
  }): Promise<ApiResponse> {
    const queryParams = {
      email,
      ...params,
    };
    const response = await apiClient.get('/vote-contestants/voter-history', queryParams);
    return response;
  }

  /**
   * Get real-time voting updates for a contest
   */
  static async getContestVotingUpdates(contestId: string, lastUpdate?: string): Promise<ApiResponse> {
    const params = lastUpdate ? { lastUpdate } : undefined;
    const response = await apiClient.get(`/contests/${contestId}/voting-updates`, params);
    return response;
  }

  /**
   * Report suspicious voting activity
   */
  static async reportSuspiciousVoting(data: {
    contestantId: string;
    paymentReference: string;
    reason: string;
    description?: string;
  }): Promise<ApiResponse> {
    const response = await apiClient.post('/vote-contestants/report-suspicious', data);
    return response;
  }

  /**
   * Get voting analytics for admin/vendor dashboard
   */
  static async getVotingAnalytics(params?: {
    contestId?: string;
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<ApiResponse> {
    const response = await apiClient.get('/vote-contestants/analytics', params);
    return response;
  }

  /**
   * Bulk vote verification (for admin purposes)
   */
  static async bulkVerifyVotes(paymentReferences: string[]): Promise<ApiResponse> {
    const response = await apiClient.post('/vote-contestants/bulk-verify', {
      paymentReferences,
    });
    return response;
  }

  /**
   * Get vote distribution for a contest
   */
  static async getVoteDistribution(contestId: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/contests/${contestId}/vote-distribution`);
    return response;
  }

  /**
   * Export voting data (CSV/Excel)
   */
  static async exportVotingData(contestId: string, format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await fetch(`${apiClient.baseUrl}/contests/${contestId}/export-votes?format=${format}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to export voting data');
    }

    return response.blob();
  }
}

export default VotingService;
