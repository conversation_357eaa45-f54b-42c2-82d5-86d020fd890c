import { apiClient, ApiResponse, setAuthToken, removeAuthToken } from '../api';

// Auth Types
export interface RegisterData {
  name: string;
  email: string;
  password: string;
  bio: string;
  file?: File;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token: string;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
}

export interface VerifyEmailData {
  otp: string;
  email: string;
}

// Auth API Service
export class AuthService {
  /**
   * Register a new client account
   */
  static async register(data: RegisterData): Promise<RegisterResponse> {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('email', data.email);
    formData.append('password', data.password);
    formData.append('bio', data.bio);

    if (data.file) {
      formData.append('file', data.file);
    }

    const response = await apiClient.post<RegisterResponse>('/auth/register', formData);
    return response;
  }

  /**
   * Login a client and receive authentication token
   */
  static async login(data: LoginData): Promise<LoginResponse> {
    const response = await apiClient.postForm<LoginResponse>('/auth/login', {
      email: data.email,
      password: data.password,
    });

    // The API returns the response directly
    // Store token if login successful
    if (response.success && response.token) {
      setAuthToken(response.token);
    }

    return response;
  }

  /**
   * Logout the currently authenticated client
   */
  static async logout(): Promise<any> {
    try {
      const response = await apiClient.post('/auth/logout');

      // Remove token regardless of response
      removeAuthToken();

      return response;
    } catch (error) {
      // Remove token even if logout fails
      removeAuthToken();
      throw error;
    }
  }

  /**
   * Verify client's email address via OTP
   */
  static async verifyEmail(data: VerifyEmailData): Promise<any> {
    const response = await apiClient.post(`/auth/verify-email?otp=${data.otp}&email=${encodeURIComponent(data.email)}`);
    return response;
  }

  /**
   * Request password reset
   */
  static async forgotPassword(email: string): Promise<any> {
    const response = await apiClient.post('/auth/forgot-password', { email });
    return response;
  }

  /**
   * Reset password with token
   */
  static async resetPassword(token: string, password: string): Promise<any> {
    const response = await apiClient.post('/auth/reset-password', { token, password });
    return response;
  }

  /**
   * Resend email verification
   */
  static async resendVerification(email: string): Promise<any> {
    const response = await apiClient.post('/auth/resend-verification', { email });
    return response;
  }



  /**
   * Update user profile
   */
  static async updateProfile(data: Partial<RegisterData>): Promise<any> {
    const formData = new FormData();
    
    if (data.name) formData.append('name', data.name);
    if (data.email) formData.append('email', data.email);
    if (data.bio) formData.append('bio', data.bio);
    if (data.file) formData.append('file', data.file);

    const response = await apiClient.put('/auth/profile', formData);
    return response;
  }

  /**
   * Change password
   */
  static async changePassword(currentPassword: string, newPassword: string): Promise<any> {
    const response = await apiClient.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
    return response;
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>('/auth/refresh');

    if (response.success && response.token) {
      setAuthToken(response.token);
    }

    return response;
  }

  /**
   * Get current user info from token
   */
  static getCurrentUser(): { id: string; email?: string } | null {
    const token = localStorage.getItem('auth_token');
    if (!token) return null;

    try {
      // Decode JWT token (basic decode, not verification)
      const payload = JSON.parse(atob(token.split('.')[1]));
      return {
        id: payload.id,
        email: payload.email,
      };
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('auth_token');
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch (error) {
      return false;
    }
  }
}

export default AuthService;
