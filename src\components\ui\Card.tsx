"use client";

import { forwardRef, HTMLAttributes, ReactNode } from "react";
import { motion, MotionProps } from "framer-motion";

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "elevated" | "outlined" | "gradient";
  size?: "sm" | "md" | "lg" | "xl";
  hover?: boolean;
  interactive?: boolean;
  glow?: "orange" | "red" | "none";
  children: ReactNode;
  motionProps?: MotionProps;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      variant = "default",
      size = "md",
      hover = false,
      interactive = false,
      glow = "none",
      children,
      className = "",
      motionProps,
      ...props
    },
    ref
  ) => {
    const baseClasses = "rounded-xl transition-all duration-300 relative overflow-hidden";

    const variantClasses = {
      default: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-modern",
      elevated: "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-modern-lg",
      outlined: "bg-transparent border-2 border-gray-300 dark:border-gray-600",
      gradient: "bg-gradient-to-br from-primary-50 to-accent-50 dark:from-gray-900 dark:to-gray-800 border border-gray-200 dark:border-gray-700 shadow-modern",
    };

    const sizeClasses = {
      sm: "p-4",
      md: "p-6",
      lg: "p-8",
      xl: "p-10",
    };

    const glowClasses = {
      orange: "shadow-orange-glow",
      red: "shadow-red-glow",
      none: "",
    };

    const hoverClasses = hover
      ? "hover:shadow-modern-xl hover:-translate-y-1 hover:scale-[1.02]"
      : "";

    const interactiveClasses = interactive
      ? "cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-black"
      : "";

    const cardClasses = `
      ${baseClasses}
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${glowClasses[glow]}
      ${hoverClasses}
      ${interactiveClasses}
      ${className}
    `.trim();

    const cardVariants = {
      initial: {
        scale: 1,
        y: 0,
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
      },
      hover: {
        scale: 1.02,
        y: -4,
        boxShadow: glow !== "none"
          ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 20px rgba(249, 115, 22, 0.3)"
          : "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25
        }
      },
      tap: {
        scale: 0.98,
        y: 0,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 20
        }
      },
    };

    if (motionProps || hover || interactive) {
      return (
        <motion.div
          ref={ref}
          className={cardClasses}
          variants={cardVariants as any}
          initial="initial"
          whileHover={hover || interactive ? "hover" : undefined}
          whileTap={interactive ? "tap" : undefined}
          {...motionProps}
          {...(props as any)}
        >
          {children}
        </motion.div>
      );
    }

    return (
      <div ref={ref} className={cardClasses} {...props}>
        {children}
      </div>
    );
  }
);

Card.displayName = "Card";

// Card Header Component
interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className = "", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`border-b border-gray-200 dark:border-gray-700 pb-4 mb-4 ${className}`}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = "CardHeader";

// Card Title Component
interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

export const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ children, as: Component = "h3", className = "", ...props }, ref) => {
    return (
      <Component
        ref={ref}
        className={`text-lg font-semibold text-text-light dark:text-text-dark ${className}`}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

CardTitle.displayName = "CardTitle";

// Card Content Component
interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ children, className = "", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`text-gray-600 dark:text-gray-400 ${className}`}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardContent.displayName = "CardContent";

// Card Footer Component
interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className = "", ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`border-t border-gray-200 dark:border-gray-700 pt-4 mt-4 ${className}`}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = "CardFooter";

export default Card;
