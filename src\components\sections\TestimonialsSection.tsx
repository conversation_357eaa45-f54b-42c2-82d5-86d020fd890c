"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronLeft, ChevronRight, Star, Quote } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const TestimonialsSection: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Marketing Director",
      company: "TechStart Inc.",
      avatar: "/api/placeholder/80/80",
      rating: 5,
      content: "Kontestica transformed how we run our annual innovation awards. The platform is intuitive, secure, and the analytics are incredibly detailed. Our participation increased by 300%!",
      contestType: "Innovation Awards",
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Event Organizer",
      company: "Creative Collective",
      avatar: "/api/placeholder/80/80",
      rating: 5,
      content: "The voting system is bulletproof and the real-time results kept our audience engaged throughout the entire contest. Customer support was exceptional when we needed help.",
      contestType: "Design Competition",
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      role: "Community Manager",
      company: "Global Arts Foundation",
      avatar: "/api/placeholder/80/80",
      rating: 5,
      content: "We've used several platforms before, but none come close to Kontestica's ease of use and feature set. The certificate generation saved us hours of manual work.",
      contestType: "Art Contest",
    },
    {
      id: 4,
      name: "David Thompson",
      role: "HR Director",
      company: "MegaCorp Solutions",
      avatar: "/api/placeholder/80/80",
      rating: 5,
      content: "Running our employee recognition program has never been easier. The platform scales beautifully and handles thousands of votes without any issues.",
      contestType: "Employee Awards",
    },
  ];

  const nextTestimonial = (): void => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = (): void => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index: number): void => {
    setCurrentIndex(index);
  };

  return (
    <AnimatedSection
      variant="primary"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="300"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-secondary-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-primary-500 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10">
        <SectionHeader>
          <SectionTitle>What Our Users Say</SectionTitle>
          <SectionSubtitle>
            Join thousands of satisfied organizers who trust Kontestica 
            to power their contests and awards.
          </SectionSubtitle>
        </SectionHeader>

        {/* Main Testimonial Slider */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="relative pt-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
              >
                <Card
                  size="lg"
                  className="text-center relative overflow-visible"
                  motionProps={{
                    whileHover: { scale: 1.02 },
                    transition: { type: "spring", stiffness: 300 }
                  }}
                >
                  {/* Quote Icon */}
                  <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-30">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center shadow-lg">
                      <Quote className="h-6 w-6 text-white" />
                    </div>
                  </div>

                  {/* Rating */}
                  <div className="flex justify-center mb-6 mt-4">
                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>

                  {/* Content */}
                  <blockquote className="text-xl lg:text-2xl text-gray-700 dark:text-gray-300 mb-8 leading-relaxed italic">
                    &ldquo;{testimonials[currentIndex].content}&rdquo;
                  </blockquote>

                  {/* Author */}
                  <div className="flex items-center justify-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-full flex items-center justify-center">
                      <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                        {testimonials[currentIndex].name.charAt(0)}
                      </span>
                    </div>
                    <div className="text-left">
                      <div className="font-semibold text-slate-900 dark:text-slate-100">
                        {testimonials[currentIndex].name}
                      </div>
                      <div className="text-slate-600 dark:text-slate-400">
                        {testimonials[currentIndex].role}
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-500">
                        {testimonials[currentIndex].company}
                      </div>
                    </div>
                  </div>

                  {/* Contest Type Badge */}
                  <div className="mt-6">
                    <span className="inline-block px-4 py-2 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                      {testimonials[currentIndex].contestType}
                    </span>
                  </div>
                </Card>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="absolute top-1/2 -translate-y-1/2 -left-4 lg:-left-16">
              <Button
                variant="ghost"
                size="lg"
                onClick={prevTestimonial}
                className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-lg"
                motionProps={{
                  whileHover: { scale: 1.1 },
                  whileTap: { scale: 0.9 }
                }}
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
            </div>

            <div className="absolute top-1/2 -translate-y-1/2 -right-4 lg:-right-16">
              <Button
                variant="ghost"
                size="lg"
                onClick={nextTestimonial}
                className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-lg"
                motionProps={{
                  whileHover: { scale: 1.1 },
                  whileTap: { scale: 0.9 }
                }}
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
            </div>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center space-x-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? "bg-primary-500 scale-125"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Stats */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto"
        >
          {[
            { value: "50K+", label: "Happy Users" },
            { value: "4.9/5", label: "Average Rating" },
            { value: "99.9%", label: "Uptime" },
          ].map((stat, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
              className="text-center"
            >
              <div className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-2">
                {stat.value}
              </div>
              <div className="text-slate-600 dark:text-slate-300">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default TestimonialsSection;
