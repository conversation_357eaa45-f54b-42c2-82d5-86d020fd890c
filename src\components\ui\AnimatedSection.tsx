"use client";

import { forwardRef, ReactNode } from "react";
import { motion, useInView, MotionProps } from "framer-motion";
import { useRef } from "react";
import Section from "./Section";

interface AnimatedSectionProps {
  children: ReactNode;
  className?: string;
  variant?: "default" | "primary" | "secondary" | "dark" | "light";
  size?: "sm" | "md" | "lg" | "xl" | "2xl";
  animation?: "fadeIn" | "slideUp" | "slideDown" | "slideLeft" | "slideRight" | "scaleIn" | "stagger";
  delay?: number;
  duration?: number;
  threshold?: number;
  triggerOnce?: boolean;
  staggerChildren?: number;
  motionProps?: MotionProps;
}

const AnimatedSection = forwardRef<HTMLElement, AnimatedSectionProps>(
  (
    {
      children,
      className = "",
      variant = "default",
      size = "lg",
      animation = "fadeIn",
      delay = 0,
      duration = 0.6,
      threshold = 0.1,
      triggerOnce = true,
      staggerChildren = 0.1,
      motionProps,
    },
    ref
  ) => {
    const sectionRef = useRef<HTMLElement>(null);
    const isInView = useInView(sectionRef, {
      amount: threshold,
      once: triggerOnce,
    });

    const animationVariants = {
      fadeIn: {
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            duration,
            delay,
            ease: "easeOut",
          },
        },
      },
      slideUp: {
        hidden: { opacity: 0, y: 50 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration,
            delay,
            ease: "easeOut",
          },
        },
      },
      slideDown: {
        hidden: { opacity: 0, y: -50 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration,
            delay,
            ease: "easeOut",
          },
        },
      },
      slideLeft: {
        hidden: { opacity: 0, x: 50 },
        visible: {
          opacity: 1,
          x: 0,
          transition: {
            duration,
            delay,
            ease: "easeOut",
          },
        },
      },
      slideRight: {
        hidden: { opacity: 0, x: -50 },
        visible: {
          opacity: 1,
          x: 0,
          transition: {
            duration,
            delay,
            ease: "easeOut",
          },
        },
      },
      scaleIn: {
        hidden: { opacity: 0, scale: 0.8 },
        visible: {
          opacity: 1,
          scale: 1,
          transition: {
            duration,
            delay,
            ease: "easeOut",
          },
        },
      },
      stagger: {
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            duration,
            delay,
            staggerChildren,
            ease: "easeOut",
          },
        },
      },
    };

    const currentVariants = animationVariants[animation];

    return (
      <Section
        ref={sectionRef}
        variant={variant}
        size={size}
        className={className}
      >
        <motion.div
          ref={ref as any}
          variants={currentVariants as any}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          {...motionProps}
        >
          {children}
        </motion.div>
      </Section>
    );
  }
);

AnimatedSection.displayName = "AnimatedSection";

// Animated Item Component for stagger animations
interface AnimatedItemProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  motionProps?: MotionProps;
}

export const AnimatedItem: React.FC<AnimatedItemProps> = ({
  children,
  className = "",
  delay = 0,
  motionProps,
}) => {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      variants={itemVariants as any}
      className={className}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
};

// Animated Text Component
interface AnimatedTextProps {
  children: ReactNode;
  className?: string;
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6" | "p" | "span" | "div";
  animation?: "typewriter" | "fadeInWords" | "slideInWords";
  delay?: number;
  duration?: number;
}

export const AnimatedText: React.FC<AnimatedTextProps> = ({
  children,
  className = "",
  as: Component = "div",
  animation = "fadeInWords",
  delay = 0,
  duration = 0.8,
}) => {
  const textRef = useRef<HTMLElement>(null);
  const isInView = useInView(textRef, { amount: 0.1, once: true });

  const text = typeof children === "string" ? children : "";
  const words = text.split(" ");

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delay,
        staggerChildren: 0.1,
        duration,
      },
    },
  };

  const wordVariants = {
    fadeInWords: {
      hidden: { opacity: 0, y: 20 },
      visible: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.5,
          ease: "easeOut",
        },
      },
    },
    slideInWords: {
      hidden: { opacity: 0, x: -20 },
      visible: {
        opacity: 1,
        x: 0,
        transition: {
          duration: 0.5,
          ease: "easeOut",
        },
      },
    },
  };

  if (animation === "typewriter") {
    return (
      <Component ref={textRef as any} className={className}>
        <motion.span
          initial={{ width: 0 }}
          animate={isInView ? { width: "auto" } : { width: 0 }}
          transition={{ duration, delay, ease: "easeInOut" }}
          style={{ overflow: "hidden", whiteSpace: "nowrap" }}
        >
          {children}
        </motion.span>
      </Component>
    );
  }

  return (
    <Component ref={textRef as any} className={className}>
      <motion.span
        variants={containerVariants as any}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        style={{ display: "inline-block" }}
      >
        {words.map((word, index) => (
          <motion.span
            key={index}
            variants={wordVariants[animation as keyof typeof wordVariants] as any}
            style={{ display: "inline-block", marginRight: "0.25em" }}
          >
            {word}
          </motion.span>
        ))}
      </motion.span>
    </Component>
  );
};

export default AnimatedSection;
