import { apiClient, ApiResponse } from '../api';

// Contestant Types
export interface Contestant {
  _id: string;
  contestID: string | {
    _id: string;
    title: string;
  };
  name: string;
  stageName: string;
  email: string;
  phone: string;
  bio: string;
  profilePictureUrl: string;
  profilePicturePublicId: string;
  paymentReference: string;
  status: 'pending' | 'approved' | 'rejected';
  voteCount: number;
  createdAt: string;
  updatedAt: string;
  slug: string;
  __v: number;
}

export interface CreateContestantData {
  name: string;
  stageName: string;
  email: string;
  phone: string;
  contestID: string;
  bio: string;
  paymentReference: string;
  contestantphoto?: File;
}

export interface UpdateContestantData extends Partial<Omit<CreateContestantData, 'contestID' | 'paymentReference'>> {
  // All fields except contestID and paymentReference are optional for updates
}

export interface ContestantResponse {
  success: boolean;
  message: string;
  contestant: Contestant;
}

export interface ContestantsListResponse {
  success: boolean;
  message: string;
  contestants: Contestant[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Contestants API Service
export class ContestantsService {
  /**
   * Register a new contestant into a contest
   */
  static async createContestant(data: CreateContestantData): Promise<ContestantResponse> {
    const formData = new FormData();
    
    // Add text fields
    formData.append('name', data.name);
    formData.append('stageName', data.stageName);
    formData.append('email', data.email);
    formData.append('phone', data.phone);
    formData.append('contestID', data.contestID);
    formData.append('bio', data.bio);
    formData.append('paymentReference', data.paymentReference);
    
    // Add photo if provided
    if (data.contestantphoto) {
      formData.append('contestantphoto', data.contestantphoto);
    }

    const response = await apiClient.post<ContestantResponse>('/contestants/', formData);
    return response;
  }

  /**
   * Update a contestant's information
   */
  static async updateContestant(contestantId: string, data: UpdateContestantData): Promise<ContestantResponse> {
    const formData = new FormData();
    
    // Add text fields if provided
    if (data.name) formData.append('name', data.name);
    if (data.stageName) formData.append('stageName', data.stageName);
    if (data.email) formData.append('email', data.email);
    if (data.phone) formData.append('phone', data.phone);
    if (data.bio) formData.append('bio', data.bio);
    
    // Add photo if provided
    if (data.contestantphoto) {
      formData.append('contestantphoto', data.contestantphoto);
    }

    const response = await apiClient.put<ContestantResponse>(`/contestants/${contestantId}`, formData);
    return response;
  }

  /**
   * Get contestant by ID
   */
  static async getContestant(contestantId: string): Promise<ContestantResponse> {
    const response = await apiClient.get<ContestantResponse>(`/contestants/${contestantId}`);
    return response;
  }

  /**
   * Get contestants by contest ID
   */
  static async getContestantsByContest(contestId: string, params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ContestantsListResponse> {
    const queryParams = {
      contestID: contestId,
      ...params,
    };
    const response = await apiClient.get<ContestantsListResponse>('/contestants/', queryParams);
    return response;
  }

  /**
   * Get all contestants created by the authenticated client
   */
  static async getClientContestants(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ContestantsListResponse> {
    const response = await apiClient.get<ContestantsListResponse>('/contestants', params);
    return response;
  }

  /**
   * Get contestant by stage name
   */
  static async getContestantByStage(stageName: string): Promise<ContestantsListResponse> {
    const response = await apiClient.get<ContestantsListResponse>(`/contestants/stage-name/${encodeURIComponent(stageName)}`);
    return response;
  }

  /**
   * Delete a contestant
   */
  static async deleteContestant(contestantId: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/contestants/${contestantId}`);
    return response;
  }

  /**
   * Approve a contestant (for contests requiring approval)
   */
  static async approveContestant(contestantId: string): Promise<ContestantResponse> {
    const response = await apiClient.post<ContestantResponse>(`/contestants/${contestantId}/approve`);
    return response;
  }

  /**
   * Reject a contestant (for contests requiring approval)
   */
  static async rejectContestant(contestantId: string, reason?: string): Promise<ContestantResponse> {
    const response = await apiClient.post<ContestantResponse>(`/contestants/${contestantId}/reject`, {
      reason,
    });
    return response;
  }

  /**
   * Get contestant statistics
   */
  static async getContestantStats(contestantId: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/contestants/${contestantId}/stats`);
    return response;
  }

  /**
   * Search contestants
   */
  static async searchContestants(query: string, params?: {
    contestId?: string;
    page?: number;
    limit?: number;
  }): Promise<ContestantsListResponse> {
    const queryParams = {
      search: query,
      ...params,
    };
    const response = await apiClient.get<ContestantsListResponse>('/contestants/search', queryParams);
    return response;
  }
}

export default ContestantsService;
