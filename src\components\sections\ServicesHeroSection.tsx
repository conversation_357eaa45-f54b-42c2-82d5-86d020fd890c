"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, CheckCircle } from "lucide-react";
import Button from "@/components/ui/Button";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const ServicesHeroSection: React.FC = () => {
  const features = [
    "Complete contest management",
    "Secure voting system",
    "Real-time analytics",
    "24/7 support",
  ];

  return (
    <AnimatedSection
      variant="primary"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-secondary-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-primary-400 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="text-center max-w-4xl mx-auto"
        >
          <motion.h1
            variants={staggerItemVariants}
            className="text-5xl sm:text-6xl lg:text-7xl font-bold leading-tight mb-6"
          >
            <span className="text-primary-950 dark:text-white">
              Everything You Need
            </span>
            <br />
            <span className="bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent">
              For Perfect Contests
            </span>
          </motion.h1>

          <motion.p
            variants={staggerItemVariants}
            className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed"
          >
            From setup to celebration, our comprehensive platform handles every aspect 
            of contest management with enterprise-grade security and user-friendly design.
          </motion.p>

          {/* Features List */}
          <motion.div
            variants={staggerItemVariants}
            className="flex flex-wrap justify-center gap-6 mb-12"
          >
            {features.map((feature, index) => (
              <motion.div
                key={index}
                variants={staggerItemVariants}
                className="flex items-center space-x-2 text-slate-700 dark:text-slate-200"
                whileHover={{ scale: 1.05 }}
              >
                <CheckCircle className="h-5 w-5 text-primary-500" />
                <span className="font-medium">{feature}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Button */}
          <motion.div
            variants={staggerItemVariants}
            className="flex items-center justify-center"
          >
            <Button
              variant="gradient"
              size="xl"
              rightIcon={<ArrowRight className="h-6 w-6" />}
              className="px-10 py-4 text-lg font-semibold shadow-xl"
              motionProps={{
                whileHover: { scale: 1.05, y: -2 },
                whileTap: { scale: 0.95 },
              }}
            >
              Get Started Free
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default ServicesHeroSection;
