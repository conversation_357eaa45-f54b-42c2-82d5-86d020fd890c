"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  ArrowLeft,
  Heart,
  Share2,
  Trophy,
  Users,
  Calendar,
  MapPin,
  Instagram,
  Twitter,
  Facebook,
  ExternalLink,
  Crown,
  TrendingUp,
  Award
} from "lucide-react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import MainContainer from "@/components/layout/MainContainer";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import VotingModal from "@/components/contest/VotingModal";

interface Contestant {
  id: string;
  name: string;
  age: number;
  location: string;
  bio: string;
  votes: number;
  image: string;
  registeredAt: string;
  featured: boolean;
  socialMedia?: {
    instagram?: string;
    twitter?: string;
    facebook?: string;
  };
  rank: number;
  voteHistory: VoteRecord[];
}

interface VoteRecord {
  id: string;
  voterName?: string;
  voteCount: number;
  timestamp: string;
  amount: number;
}

interface Contest {
  id: string;
  title: string;
  votePrice: string;
  status: "active" | "upcoming" | "voting" | "completed";
  deadline: string;
  totalVotes: number;
  totalContestants: number;
}

// Mock data - replace with API calls
const mockContestant: Contestant = {
  id: "1",
  name: "Adunni Adeola",
  age: 23,
  location: "Lagos State",
  bio: "A passionate advocate for women's education and environmental sustainability. Currently pursuing a Master's degree in International Relations at the University of Lagos. I believe in using my platform to inspire young women across Nigeria to pursue their dreams and make a positive impact in their communities.",
  votes: 1247,
  image: "/api/placeholder/400/600",
  registeredAt: "2024-11-15",
  featured: true,
  socialMedia: {
    instagram: "@adunni_adeola",
    twitter: "@adunni_speaks",
    facebook: "Adunni Adeola Official"
  },
  rank: 2,
  voteHistory: [
    {
      id: "1",
      voterName: "Anonymous Voter",
      voteCount: 5,
      timestamp: "2024-12-01T10:30:00Z",
      amount: 500
    },
    {
      id: "2",
      voterName: "John Doe",
      voteCount: 10,
      timestamp: "2024-12-01T09:15:00Z",
      amount: 1000
    },
    {
      id: "3",
      voterName: "Anonymous Voter",
      voteCount: 3,
      timestamp: "2024-11-30T18:45:00Z",
      amount: 300
    }
  ]
};

const mockContest: Contest = {
  id: "1",
  title: "Face of Nigeria 2024",
  votePrice: "100",
  status: "active",
  deadline: "2024-12-31",
  totalVotes: 15420,
  totalContestants: 156
};

export default function ContestantPage() {
  const params = useParams();
  const router = useRouter();
  const [contestant, setContestant] = useState<Contestant | null>(null);
  const [contest, setContest] = useState<Contest | null>(null);
  const [loading, setLoading] = useState(true);
  const [showVoting, setShowVoting] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false); // Replace with actual auth state

  useEffect(() => {
    // Simulate API calls
    const fetchData = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setContestant(mockContestant);
      setContest(mockContest);
      setLoading(false);
    };

    fetchData();
  }, [params.id, params.contestantId]);

  const handleShare = async () => {
    if (navigator.share && contestant && contest) {
      try {
        await navigator.share({
          title: `Vote for ${contestant.name} in ${contest.title}`,
          text: `Support ${contestant.name} in ${contest.title}! Cast your vote now.`,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric"
    });
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(dateString);
  };

  if (loading) {
    return (
      <MainContainer>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </MainContainer>
    );
  }

  if (!contestant || !contest) {
    return (
      <MainContainer>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card className="text-center py-12">
            <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Contestant Not Found
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The contestant you're looking for doesn't exist or has been removed.
            </p>
            <Button
              variant="outline"
              leftIcon={<ArrowLeft className="w-4 h-4" />}
              onClick={() => router.push(`/contests/${params.id}`)}
            >
              Back to Contest
            </Button>
          </Card>
        </div>
      </MainContainer>
    );
  }

  return (
    <MainContainer>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <Button
          variant="ghost"
          leftIcon={<ArrowLeft className="w-4 h-4" />}
          onClick={() => router.push(`/contests/${params.id}`)}
          className="mb-6"
        >
          Back to {contest.title}
        </Button>

        {/* Contestant Hero Section */}
        <Card className="mb-8 overflow-hidden">
          <div className="relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 via-secondary-500/10 to-primary-500/10"></div>
            
            <div className="relative p-8">
              <div className="flex flex-col lg:flex-row items-start lg:items-center gap-8">
                {/* Contestant Image */}
                <div className="relative">
                  <div className="w-48 h-64 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-xl overflow-hidden shadow-xl">
                    <div className="w-full h-full flex items-center justify-center">
                      <Users className="w-20 h-20 text-gray-400" />
                    </div>
                  </div>
                  
                  {/* Rank Badge */}
                  <div className="absolute -top-3 -right-3">
                    <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold shadow-lg">
                      #{contestant.rank}
                    </div>
                  </div>
                  
                  {/* Featured Badge */}
                  {contestant.featured && (
                    <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg">
                        <Crown className="w-3 h-3 mr-1" />
                        Featured
                      </span>
                    </div>
                  )}
                </div>
                
                {/* Contestant Info */}
                <div className="flex-1">
                  <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {contestant.name}
                  </h1>
                  
                  <div className="flex items-center gap-4 text-gray-600 dark:text-gray-400 mb-4">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      Age {contestant.age}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      {contestant.location}
                    </div>
                  </div>
                  
                  {/* Vote Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                        {contestant.votes.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Total Votes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-secondary-600 dark:text-secondary-400">
                        #{contestant.rank}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Current Rank</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {Math.round((contestant.votes / contest.totalVotes) * 100)}%
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Vote Share</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {contestant.voteHistory.length}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Supporters</div>
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex flex-wrap gap-3">
                    <Button
                      variant="gradient"
                      size="lg"
                      leftIcon={<Heart className="w-5 h-5" />}
                      onClick={() => setShowVoting(true)}
                      glow={true}
                      motionProps={{
                        whileHover: { scale: 1.05, y: -2 },
                        whileTap: { scale: 0.95 }
                      }}
                    >
                      Vote Now
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="lg"
                      leftIcon={<Share2 className="w-5 h-5" />}
                      onClick={handleShare}
                      motionProps={{
                        whileHover: { scale: 1.02 },
                        whileTap: { scale: 0.98 }
                      }}
                    >
                      Share
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Bio Section */}
            <Card>
              <div className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  About {contestant.name}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {contestant.bio}
                </p>
              </div>
            </Card>

            {/* Social Media */}
            {contestant.socialMedia && (
              <Card>
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                    Connect with {contestant.name}
                  </h2>
                  <div className="flex flex-wrap gap-3">
                    {contestant.socialMedia.instagram && (
                      <Button
                        variant="outline"
                        leftIcon={<Instagram className="w-4 h-4" />}
                        rightIcon={<ExternalLink className="w-4 h-4" />}
                        className="text-pink-600 border-pink-600 hover:bg-pink-50 dark:hover:bg-pink-900/20"
                      >
                        {contestant.socialMedia.instagram}
                      </Button>
                    )}
                    {contestant.socialMedia.twitter && (
                      <Button
                        variant="outline"
                        leftIcon={<Twitter className="w-4 h-4" />}
                        rightIcon={<ExternalLink className="w-4 h-4" />}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                      >
                        {contestant.socialMedia.twitter}
                      </Button>
                    )}
                    {contestant.socialMedia.facebook && (
                      <Button
                        variant="outline"
                        leftIcon={<Facebook className="w-4 h-4" />}
                        rightIcon={<ExternalLink className="w-4 h-4" />}
                        className="text-blue-700 border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                      >
                        {contestant.socialMedia.facebook}
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Vote */}
            <Card>
              <div className="p-6 text-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Support {contestant.name}
                </h3>
                <div className="mb-4">
                  <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-1">
                    ₦{contest.votePrice}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">per vote</div>
                </div>
                <Button
                  variant="gradient"
                  fullWidth
                  leftIcon={<Heart className="w-4 h-4" />}
                  onClick={() => setShowVoting(true)}
                  glow={true}
                >
                  Cast Your Vote
                </Button>
              </div>
            </Card>

            {/* Recent Votes */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-green-500" />
                  Recent Support
                </h3>
                <div className="space-y-3">
                  {contestant.voteHistory.slice(0, 5).map((vote) => (
                    <div key={vote.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                          {vote.voterName || "Anonymous Supporter"}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTimeAgo(vote.timestamp)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-primary-600 dark:text-primary-400 text-sm">
                          {vote.voteCount} vote{vote.voteCount > 1 ? 's' : ''}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          ₦{vote.amount.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Contest Info */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Contest Details
                </h3>
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Contest:</span>
                    <span className="text-gray-900 dark:text-gray-100 font-medium">{contest.title}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Status:</span>
                    <span className="text-green-600 dark:text-green-400 font-medium capitalize">{contest.status}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Deadline:</span>
                    <span className="text-gray-900 dark:text-gray-100 font-medium">{formatDate(contest.deadline)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Total Contestants:</span>
                    <span className="text-gray-900 dark:text-gray-100 font-medium">{contest.totalContestants}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Total Votes:</span>
                    <span className="text-gray-900 dark:text-gray-100 font-medium">{contest.totalVotes.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Voting Modal */}
        <VotingModal
          contestant={contestant}
          contest={contest}
          isOpen={showVoting}
          onClose={() => setShowVoting(false)}
          isLoggedIn={isLoggedIn}
          onLoginRequired={() => {
            // Redirect to login page
            router.push('/login');
          }}
          onSuccess={(voteData) => {
            console.log('Vote successful:', voteData);
            setShowVoting(false);
            // Update contestant votes in real app
          }}
        />
      </div>
    </MainContainer>
  );
}
