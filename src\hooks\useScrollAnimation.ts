"use client";

import { useRef, useEffect, useState } from "react";
import { useInView, useScroll, useTransform } from "framer-motion";

// Hook for basic scroll-triggered animations
export const useScrollAnimation = (
  threshold: number = 0.1,
  triggerOnce: boolean = true
) => {
  const ref = useRef<HTMLElement>(null);
  const isInView = useInView(ref, {
    amount: threshold,
    once: triggerOnce,
  });

  return { ref, isInView };
};

// Hook for scroll progress
export const useScrollProgress = () => {
  const { scrollYProgress } = useScroll();
  return scrollYProgress;
};

// Hook for element-specific scroll progress
export const useElementScrollProgress = () => {
  const ref = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  return { ref, scrollYProgress };
};

// Hook for parallax effect
export const useParallax = (distance: number = 50) => {
  const ref = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], [-distance, distance]);

  return { ref, y };
};

// Hook for scale on scroll
export const useScaleOnScroll = (
  scaleRange: [number, number] = [0.8, 1]
) => {
  const ref = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [scaleRange[0], 1, scaleRange[1]]);

  return { ref, scale };
};

// Hook for opacity on scroll
export const useOpacityOnScroll = (
  opacityRange: [number, number] = [0, 1]
) => {
  const ref = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const opacity = useTransform(
    scrollYProgress, 
    [0, 0.2, 0.8, 1], 
    [opacityRange[0], opacityRange[1], opacityRange[1], opacityRange[0]]
  );

  return { ref, opacity };
};

// Hook for rotation on scroll
export const useRotateOnScroll = (
  rotationRange: [number, number] = [0, 360]
) => {
  const ref = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const rotate = useTransform(scrollYProgress, [0, 1], rotationRange);

  return { ref, rotate };
};

// Hook for text reveal animation
export const useTextReveal = (
  threshold: number = 0.1,
  staggerDelay: number = 0.1
) => {
  const ref = useRef<HTMLElement>(null);
  const isInView = useInView(ref, {
    amount: threshold,
    once: true,
  });

  const [revealedWords, setRevealedWords] = useState<number>(0);

  useEffect(() => {
    if (isInView && ref.current) {
      const text = ref.current.textContent || "";
      const words = text.split(" ");
      
      words.forEach((_, index) => {
        setTimeout(() => {
          setRevealedWords(index + 1);
        }, index * staggerDelay * 1000);
      });
    }
  }, [isInView, staggerDelay]);

  return { ref, isInView, revealedWords };
};

// Hook for scroll direction
export const useScrollDirection = () => {
  const [scrollDirection, setScrollDirection] = useState<"up" | "down" | null>(null);
  const [lastScrollY, setLastScrollY] = useState<number>(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY) {
        setScrollDirection("down");
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection("up");
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  return scrollDirection;
};

// Hook for scroll-based counter animation
export const useCounterAnimation = (
  endValue: number,
  duration: number = 2000,
  threshold: number = 0.1
) => {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, {
    amount: threshold,
    once: true,
  });

  const [count, setCount] = useState<number>(0);

  useEffect(() => {
    if (isInView) {
      let startTime: number | null = null;
      const startValue = 0;

      const animate = (currentTime: number) => {
        if (startTime === null) startTime = currentTime;
        const progress = Math.min((currentTime - startTime) / duration, 1);
        
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentCount = Math.floor(startValue + (endValue - startValue) * easeOutQuart);
        
        setCount(currentCount);

        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    }
  }, [isInView, endValue, duration]);

  return { ref, count, isInView };
};

// Hook for staggered children animation
export const useStaggeredAnimation = (
  childrenCount: number,
  staggerDelay: number = 0.1,
  threshold: number = 0.1
) => {
  const ref = useRef<HTMLElement>(null);
  const isInView = useInView(ref, {
    amount: threshold,
    once: true,
  });

  const [visibleChildren, setVisibleChildren] = useState<number>(0);

  useEffect(() => {
    if (isInView) {
      for (let i = 0; i < childrenCount; i++) {
        setTimeout(() => {
          setVisibleChildren(i + 1);
        }, i * staggerDelay * 1000);
      }
    }
  }, [isInView, childrenCount, staggerDelay]);

  return { ref, visibleChildren, isInView };
};

// Hook for mouse-based parallax
export const useMouseParallax = (strength: number = 0.1) => {
  const ref = useRef<HTMLElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [elementPosition, setElementPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        const deltaX = (e.clientX - centerX) * strength;
        const deltaY = (e.clientY - centerY) * strength;
        
        setMousePosition({ x: e.clientX, y: e.clientY });
        setElementPosition({ x: deltaX, y: deltaY });
      }
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [strength]);

  return { ref, mousePosition, elementPosition };
};

// Hook for scroll-triggered timeline
export const useScrollTimeline = (
  keyframes: Array<{ offset: number; values: Record<string, any> }>
) => {
  const ref = useRef<HTMLElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const [currentValues, setCurrentValues] = useState<Record<string, any>>({});

  useEffect(() => {
    const unsubscribe = scrollYProgress.onChange((progress) => {
      // Find the current keyframe based on scroll progress
      let currentKeyframe = keyframes[0];
      
      for (let i = 0; i < keyframes.length; i++) {
        if (progress >= keyframes[i].offset) {
          currentKeyframe = keyframes[i];
        } else {
          break;
        }
      }
      
      setCurrentValues(currentKeyframe.values);
    });

    return unsubscribe;
  }, [scrollYProgress, keyframes]);

  return { ref, currentValues, scrollYProgress };
};
