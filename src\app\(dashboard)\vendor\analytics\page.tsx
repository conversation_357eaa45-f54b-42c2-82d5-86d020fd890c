"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  TrendingUp, 
  Users, 
  DollarSign, 
  Trophy, 
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award
} from "lucide-react";
import Card from "@/components/ui/Card";
import { ContestsService } from "@/lib/api/contests";
import { handleApiError } from "@/lib/api";

interface AnalyticsData {
  totalContests: number;
  activeContests: number;
  totalContestants: number;
  totalVotes: number;
  totalRevenue: number;
  monthlyRevenue: number[];
  contestPerformance: {
    contestId: string;
    title: string;
    contestants: number;
    votes: number;
    revenue: number;
  }[];
  recentActivity: {
    type: string;
    description: string;
    timestamp: string;
  }[];
}

export default function VendorAnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch contests and calculate analytics
        const contests = await ContestsService.getClientContests();
        
        // Calculate analytics from contests data
        const totalContests = contests.length;
        const activeContests = contests.filter(c => c.status === "active").length;
        const totalContestants = contests.reduce((sum, c) => sum + (c.contestants || 0), 0);
        const totalVotes = contests.reduce((sum, c) => sum + (c.votes || 0), 0);
        const totalRevenue = contests.reduce((sum, c) => sum + ((c.contestants || 0) * c.registrationFee), 0);
        
        // Mock monthly revenue data (replace with actual API call)
        const monthlyRevenue = [120000, 150000, 180000, 220000, 190000, 250000, 280000, 320000, 290000, 350000, 380000, 420000];
        
        // Contest performance data
        const contestPerformance = contests.slice(0, 5).map(contest => ({
          contestId: contest._id,
          title: contest.title,
          contestants: contest.contestants || 0,
          votes: contest.votes || 0,
          revenue: (contest.contestants || 0) * contest.registrationFee
        }));
        
        // Mock recent activity (replace with actual API call)
        const recentActivity = [
          {
            type: "registration",
            description: "New contestant registered for Face of Nigeria 2024",
            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
          },
          {
            type: "vote",
            description: "100 votes cast in Miss University Lagos",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString()
          },
          {
            type: "contest",
            description: "Mr. Handsome Nigeria contest completed",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
          }
        ];
        
        setAnalytics({
          totalContests,
          activeContests,
          totalContestants,
          totalVotes,
          totalRevenue,
          monthlyRevenue,
          contestPerformance,
          recentActivity
        });
      } catch (err: any) {
        console.error("Error fetching analytics:", err);
        setError(handleApiError(err));
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Error loading analytics</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{error}</p>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">No analytics data available</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track your contest performance and revenue
          </p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Contests</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.totalContests}</p>
              </div>
              <Trophy className="w-8 h-8 text-primary-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Contests</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.activeContests}</p>
              </div>
              <Activity className="w-8 h-8 text-green-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Contestants</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.totalContestants}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Votes</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.totalVotes.toLocaleString()}</p>
              </div>
              <Target className="w-8 h-8 text-purple-500" />
            </div>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Revenue</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">₦{analytics.totalRevenue.toLocaleString()}</p>
              </div>
              <DollarSign className="w-8 h-8 text-orange-500" />
            </div>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Chart */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Monthly Revenue
            </h3>
            <div className="h-64 flex items-end justify-between gap-2">
              {analytics.monthlyRevenue.map((revenue, index) => {
                const height = (revenue / Math.max(...analytics.monthlyRevenue)) * 100;
                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div 
                      className="w-full bg-gradient-to-t from-primary-500 to-secondary-500 rounded-t"
                      style={{ height: `${height}%` }}
                    ></div>
                    <span className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                      {new Date(2024, index).toLocaleDateString('en', { month: 'short' })}
                    </span>
                  </div>
                );
              })}
            </div>
          </Card>

          {/* Contest Performance */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Award className="w-5 h-5" />
              Top Performing Contests
            </h3>
            <div className="space-y-4">
              {analytics.contestPerformance.map((contest, index) => (
                <div key={contest.contestId} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">{contest.title}</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {contest.contestants} contestants • {contest.votes} votes
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600 dark:text-green-400">
                      ₦{contest.revenue.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Recent Activity
          </h3>
          <div className="space-y-4">
            {analytics.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border-l-4 border-primary-500 bg-primary-50 dark:bg-primary-900/20">
                <div className="flex-1">
                  <p className="text-sm text-gray-900 dark:text-white">{activity.description}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {new Date(activity.timestamp).toLocaleString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
