"use client";

import { motion } from "framer-motion";
import { Trophy, Users, Globe, Award } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import { useCounterAnimation } from "@/hooks/useScrollAnimation";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const MilestonesSection: React.FC = () => {
  const milestones = [
    {
      icon: Users,
      value: 50000,
      suffix: "+",
      label: "Active Users",
      description: "Organizers trust our platform",
    },
    {
      icon: Trophy,
      value: 25000,
      suffix: "+",
      label: "Contests Created",
      description: "Successful contests launched",
    },
    {
      icon: Globe,
      value: 120,
      suffix: "+",
      label: "Countries",
      description: "Global reach and impact",
    },
    {
      icon: Award,
      value: 99,
      suffix: ".9%",
      label: "Uptime",
      description: "Reliable platform performance",
    },
  ];

  return (
    <AnimatedSection
      variant="primary"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-secondary-500 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-primary-400 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10">
        <SectionHeader>
          <SectionTitle>Our Achievements</SectionTitle>
          <SectionSubtitle>
            Numbers that reflect our commitment to excellence and the trust our users place in us.
          </SectionSubtitle>
        </SectionHeader>

        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {milestones.map((milestone, index) => (
            <MilestoneCard key={index} milestone={milestone} index={index} />
          ))}
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

interface MilestoneCardProps {
  milestone: {
    icon: React.ComponentType<{ className?: string }>;
    value: number;
    suffix: string;
    label: string;
    description: string;
  };
  index: number;
}

const MilestoneCard: React.FC<MilestoneCardProps> = ({ milestone }) => {
  const { ref, count } = useCounterAnimation(milestone.value, 2000, 0.1);

  return (
    <motion.div
      ref={ref}
      variants={staggerItemVariants}
      className="text-center group"
      whileHover={{ scale: 1.05 }}
    >
      <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-white/10 backdrop-blur-sm rounded-2xl mb-4 group-hover:bg-primary-200 dark:group-hover:bg-white/20 transition-colors duration-300">
        <milestone.icon className="h-8 w-8 text-primary-600 dark:text-white" />
      </div>

      <div className="text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-2">
        {count.toLocaleString()}{milestone.suffix}
      </div>

      <h3 className="text-xl font-semibold text-slate-800 dark:text-white mb-2">
        {milestone.label}
      </h3>

      <p className="text-slate-600 dark:text-slate-300 text-sm">
        {milestone.description}
      </p>
    </motion.div>
  );
};

export default MilestonesSection;
