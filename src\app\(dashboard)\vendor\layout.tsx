"use client";

import { ReactNode } from "react";
import VendorSidebar from "@/components/dashboard/VendorSidebar";
import VendorHeader from "@/components/dashboard/VendorHeader";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

interface VendorLayoutProps {
  children: ReactNode;
}

export default function VendorLayout({ children }: VendorLayoutProps) {
  return (
    <ProtectedRoute requiredRole="vendor">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex h-screen overflow-hidden">
          {/* Sidebar */}
          <VendorSidebar />

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <VendorHeader />

            {/* Page Content */}
            <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
              {children}
            </main>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
