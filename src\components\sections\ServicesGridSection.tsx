"use client";

import { motion } from "framer-motion";
import { 
  PlusCircle, 
  Award, 
  CreditCard, 
  BarChart3, 
  Shield, 
  Users, 
  Zap, 
  Globe,
  ArrowRight 
} from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const ServicesGridSection: React.FC = () => {
  const services = [
    {
      icon: PlusCircle,
      title: "Contest Setup",
      description: "Create and customize contests with our intuitive builder. Set rules, categories, and branding in minutes.",
      features: ["Drag & drop builder", "Custom branding", "Multiple categories", "Rule templates"],
      color: "from-blue-500 to-cyan-500",
      popular: false,
    },
    {
      icon: Award,
      title: "Award Nomination",
      description: "Streamline the nomination process with automated workflows and smart submission management.",
      features: ["Smart forms", "Auto-moderation", "Bulk management", "Deadline tracking"],
      color: "from-purple-500 to-pink-500",
      popular: true,
    },
    {
      icon: CreditCard,
      title: "Paid Secure Voting",
      description: "Implement secure voting with multiple payment options and fraud prevention systems.",
      features: ["Paystack integration", "Fraud detection", "Multiple auth methods", "Vote verification"],
      color: "from-green-500 to-emerald-500",
      popular: false,
    },
    {
      icon: BarChart3,
      title: "Real-time Analytics",
      description: "Get detailed insights with live dashboards, voting patterns, and engagement metrics.",
      features: ["Live dashboards", "Export reports", "Voting analytics", "Engagement metrics"],
      color: "from-orange-500 to-red-500",
      popular: false,
    },
  ];

  const additionalFeatures = [
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security with encryption, compliance, and audit trails.",
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Invite team members with role-based permissions and workflows.",
    },
    {
      icon: Zap,
      title: "API Integration",
      description: "Connect with your existing tools via our comprehensive REST API.",
    },
    {
      icon: Globe,
      title: "Global Support",
      description: "Multi-language support and 24/7 customer service worldwide.",
    },
  ];

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="100"
    >
      <SectionHeader>
        <SectionTitle gradient>Our Core Services</SectionTitle>
        <SectionSubtitle>
          Everything you need to run successful contests, from creation to celebration.
        </SectionSubtitle>
      </SectionHeader>

      {/* Main Services Grid */}
      <motion.div
        variants={staggerContainerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16"
      >
        {services.map((service, index) => (
          <motion.div
            key={index}
            variants={staggerItemVariants}
            className="relative"
          >
            {service.popular && (
              <div className="absolute -top-4 left-6 z-10">
                <span className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
            )}

            <Card
              hover
              interactive
              className="h-full relative overflow-hidden group"
              motionProps={{
                whileHover: { y: -8 },
                transition: { type: "spring", stiffness: 300 }
              }}
            >
              {/* Background Gradient */}
              <div className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${service.color} opacity-10 rounded-full blur-2xl group-hover:opacity-20 transition-opacity duration-300`} />

              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${service.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <service.icon className="h-8 w-8 text-white" />
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
                {service.title}
              </h3>

              <p className="text-slate-600 dark:text-slate-300 mb-6 leading-relaxed">
                {service.description}
              </p>

              {/* Features */}
              <ul className="space-y-3 mb-6">
                {service.features.map((feature, featureIndex) => (
                  <li
                    key={featureIndex}
                    className="flex items-center text-sm text-slate-600 dark:text-slate-300"
                  >
                    <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mr-3" />
                    {feature}
                  </li>
                ))}
              </ul>

              {/* CTA */}
              <Button
                variant="outline"
                size="sm"
                rightIcon={<ArrowRight className="h-4 w-4" />}
                className="w-full group-hover:bg-primary-500 group-hover:text-white group-hover:border-primary-500 transition-all duration-300"
              >
                Learn More
              </Button>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Additional Features */}
      <motion.div
        variants={staggerContainerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {additionalFeatures.map((feature, index) => (
          <motion.div
            key={index}
            variants={staggerItemVariants}
          >
            <Card
              hover
              className="text-center h-full group"
              motionProps={{
                whileHover: { scale: 1.02 },
                transition: { type: "spring", stiffness: 300 }
              }}
            >
              <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-lg mb-4 group-hover:bg-primary-500 group-hover:text-white transition-all duration-300">
                <feature.icon className="h-6 w-6 text-primary-600 dark:text-primary-400 group-hover:text-white" />
              </div>
              
              <h4 className="text-lg font-semibold text-text-light dark:text-text-dark mb-2">
                {feature.title}
              </h4>
              
              <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                {feature.description}
              </p>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </AnimatedSection>
  );
};

export default ServicesGridSection;
