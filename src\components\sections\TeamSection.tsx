"use client";

import { motion } from "framer-motion";
import { Linkedin, Twitter, Github } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const TeamSection: React.FC = () => {
  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Co-Founder",
      bio: "Former product manager at tech giants, passionate about democratizing contest management.",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#",
      },
    },
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON> & Co-Founder",
      bio: "Full-stack engineer with 10+ years experience building scalable platforms.",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#",
      },
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      bio: "UX designer focused on creating intuitive and delightful user experiences.",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#",
      },
    },
    {
      name: "<PERSON>",
      role: "Head of Engineering",
      bio: "Backend specialist ensuring our platform scales to millions of users.",
      social: {
        linkedin: "#",
        twitter: "#",
        github: "#",
      },
    },
  ];

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
    >
      <SectionHeader>
        <SectionTitle gradient>Meet Our Team</SectionTitle>
        <SectionSubtitle>
          The passionate individuals behind Kontestica, working to make contests better for everyone.
        </SectionSubtitle>
      </SectionHeader>

      <motion.div
        variants={staggerContainerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
      >
        {team.map((member, index) => (
          <motion.div
            key={index}
            variants={staggerItemVariants}
          >
            <Card
              hover
              interactive
              className="text-center h-full"
              motionProps={{
                whileHover: { y: -8 },
                transition: { type: "spring", stiffness: 300 }
              }}
            >
              {/* Avatar */}
              <div className="w-24 h-24 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-primary-900/30 dark:to-secondary-900/30 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  {member.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>

              {/* Info */}
              <h3 className="text-xl font-bold text-text-light dark:text-text-dark mb-1">
                {member.name}
              </h3>
              <p className="text-primary-600 dark:text-primary-400 font-medium mb-3">
                {member.role}
              </p>
              <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed mb-4">
                {member.bio}
              </p>

              {/* Social Links */}
              <div className="flex justify-center space-x-3">
                <a
                  href={member.social.linkedin}
                  className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-primary-500 dark:hover:bg-primary-500 text-gray-600 dark:text-gray-400 hover:text-white transition-all duration-200"
                >
                  <Linkedin className="h-4 w-4" />
                </a>
                <a
                  href={member.social.twitter}
                  className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-primary-500 dark:hover:bg-primary-500 text-gray-600 dark:text-gray-400 hover:text-white transition-all duration-200"
                >
                  <Twitter className="h-4 w-4" />
                </a>
                <a
                  href={member.social.github}
                  className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-primary-500 dark:hover:bg-primary-500 text-gray-600 dark:text-gray-400 hover:text-white transition-all duration-200"
                >
                  <Github className="h-4 w-4" />
                </a>
              </div>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </AnimatedSection>
  );
};

export default TeamSection;
