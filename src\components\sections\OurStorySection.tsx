"use client";

import { motion } from "framer-motion";
import { Lightbulb, Rocket, Globe, Award } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const OurStorySection: React.FC = () => {
  const timeline = [
    {
      year: "2020",
      icon: Lightbulb,
      title: "The Idea",
      description: "Founded with a vision to make contest management accessible to everyone, from small communities to large organizations.",
    },
    {
      year: "2021",
      icon: Rocket,
      title: "First Launch",
      description: "Launched our MVP with basic contest creation and voting features. Our first 100 users helped shape the platform.",
    },
    {
      year: "2022",
      icon: Globe,
      title: "Global Expansion",
      description: "Expanded internationally and added multi-language support. Reached 10,000 active users across 50 countries.",
    },
    {
      year: "2024",
      icon: Award,
      title: "Industry Leader",
      description: "Now serving 50,000+ users with advanced features like AI-powered analytics and enterprise-grade security.",
    },
  ];

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
    >
      <SectionHeader>
        <SectionTitle gradient>Our Story</SectionTitle>
        <SectionSubtitle>
          From a simple idea to a global platform trusted by thousands of organizers worldwide.
        </SectionSubtitle>
      </SectionHeader>

      <div className="max-w-4xl mx-auto">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="space-y-8"
        >
          {timeline.map((item, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
              className="relative"
            >
              {/* Timeline Line */}
              {index < timeline.length - 1 && (
                <div className="absolute left-8 top-20 w-0.5 h-16 bg-gradient-to-b from-primary-500 to-secondary-500 hidden md:block" />
              )}

              <Card
                hover
                className="md:flex items-center space-y-4 md:space-y-0 md:space-x-6"
              >
                {/* Year & Icon */}
                <div className="flex items-center space-x-4 md:flex-col md:space-x-0 md:space-y-2 md:w-32 flex-shrink-0">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center">
                    <item.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {item.year}
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-3">
                    {item.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Mission Statement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <Card size="lg" className="bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20">
            <h3 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
              Our Mission
            </h3>
            <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
              To democratize contest management and make it easy for anyone to create, 
              manage, and celebrate achievements through secure, engaging, and innovative 
              digital experiences.
            </p>
          </Card>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default OurStorySection;
