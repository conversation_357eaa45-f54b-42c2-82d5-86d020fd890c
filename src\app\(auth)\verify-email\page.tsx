"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import Link from "next/link";
import { CheckCircle, AlertCircle, Mail, RefreshCw, Clock } from "lucide-react";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";

type VerificationState = "loading" | "success" | "error" | "expired" | "pending";

export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const email = searchParams.get("email");
  
  const [verificationState, setVerificationState] = useState<VerificationState>("loading");
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    if (token) {
      verifyEmail(token);
    } else {
      setVerificationState("pending");
    }
  }, [token]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      // TODO: Integrate with backend API
      console.log("Verifying email with token:", verificationToken);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate different responses
      const responses = ["success", "error", "expired"];
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      
      switch (randomResponse) {
        case "success":
          setVerificationState("success");
          break;
        case "expired":
          setVerificationState("expired");
          setErrorMessage("Verification link has expired. Please request a new one.");
          break;
        default:
          setVerificationState("error");
          setErrorMessage("Invalid verification token. Please try again or request a new verification email.");
      }
      
    } catch (error) {
      console.error("Email verification error:", error);
      setVerificationState("error");
      setErrorMessage("Failed to verify email. Please try again.");
    }
  };

  const handleResendVerification = async () => {
    if (!email) {
      setErrorMessage("Email address not found. Please register again.");
      return;
    }

    setIsResending(true);
    
    try {
      // TODO: Integrate with backend API
      console.log("Resending verification email to:", email);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setResendCooldown(60); // 60 second cooldown
      setErrorMessage("");
      
    } catch (error) {
      console.error("Resend verification error:", error);
      setErrorMessage("Failed to resend verification email. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const renderContent = () => {
    switch (verificationState) {
      case "loading":
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
            >
              <RefreshCw className="h-8 w-8 text-white" />
            </motion.div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Verifying Your Email
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Please wait while we verify your email address...
            </p>
          </motion.div>
        );

      case "success":
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
            >
              <CheckCircle className="h-8 w-8 text-white" />
            </motion.div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Email Verified Successfully!
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              Your email has been verified. You can now sign in to your account.
            </p>
            <Link href="/login">
              <Button variant="gradient" size="lg" fullWidth>
                Sign In to Your Account
              </Button>
            </Link>
          </motion.div>
        );

      case "error":
      case "expired":
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
            >
              <AlertCircle className="h-8 w-8 text-white" />
            </motion.div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Verification Failed
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              {errorMessage}
            </p>
            <div className="space-y-4">
              {email && (
                <Button
                  onClick={handleResendVerification}
                  variant="gradient"
                  size="lg"
                  fullWidth
                  isLoading={isResending}
                  loadingText="Sending..."
                  disabled={resendCooldown > 0}
                >
                  {resendCooldown > 0 ? (
                    <>
                      <Clock className="h-5 w-5 mr-2" />
                      Resend in {resendCooldown}s
                    </>
                  ) : (
                    "Resend Verification Email"
                  )}
                </Button>
              )}
              <Link href="/register">
                <Button variant="outline" size="lg" fullWidth>
                  Register Again
                </Button>
              </Link>
              <Link href="/login">
                <Button variant="ghost" size="lg" fullWidth>
                  Back to Sign In
                </Button>
              </Link>
            </div>
          </motion.div>
        );

      case "pending":
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6"
            >
              <Mail className="h-8 w-8 text-white" />
            </motion.div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Check Your Email
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.
            </p>
            
            <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-8">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Didn't receive the email?</strong> Check your spam folder or click the button below to resend.
              </p>
            </div>

            <div className="space-y-4">
              {email && (
                <Button
                  onClick={handleResendVerification}
                  variant="outline"
                  size="lg"
                  fullWidth
                  isLoading={isResending}
                  loadingText="Sending..."
                  disabled={resendCooldown > 0}
                >
                  {resendCooldown > 0 ? (
                    <>
                      <Clock className="h-5 w-5 mr-2" />
                      Resend in {resendCooldown}s
                    </>
                  ) : (
                    "Resend Verification Email"
                  )}
                </Button>
              )}
              <Link href="/login">
                <Button variant="ghost" size="lg" fullWidth>
                  Back to Sign In
                </Button>
              </Link>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      {renderContent()}
    </div>
  );
}
