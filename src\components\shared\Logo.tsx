"use client";

import { motion } from "framer-motion";
import { Trophy, Crown } from "lucide-react";

interface LogoProps {
  className?: string;
  showText?: boolean;
  variant?: "default" | "icon-only" | "text-only";
  size?: "sm" | "md" | "lg" | "xl";
}

const Logo: React.FC<LogoProps> = ({
  className = "",
  showText = true,
  variant = "default",
  size = "md",
}) => {
  const sizeClasses = {
    sm: {
      container: "h-6",
      icon: "h-6 w-6",
      text: "text-lg",
    },
    md: {
      container: "h-8",
      icon: "h-8 w-8",
      text: "text-xl",
    },
    lg: {
      container: "h-10",
      icon: "h-10 w-10",
      text: "text-2xl",
    },
    xl: {
      container: "h-12",
      icon: "h-12 w-12",
      text: "text-3xl",
    },
  };

  const currentSize = sizeClasses[size];

  const iconVariants = {
    initial: { scale: 1, rotate: 0 },
    hover: { 
      scale: 1.1, 
      rotate: 5,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 10 
      }
    },
  };

  const textVariants = {
    initial: { opacity: 1 },
    hover: { 
      opacity: 0.8,
      transition: { duration: 0.2 }
    },
  };

  if (variant === "icon-only") {
    return (
      <motion.div
        variants={iconVariants as any}
        initial="initial"
        whileHover="hover"
        className={`relative ${currentSize.container} ${className}`}
      >
        <div className="relative">
          {/* Background Circle */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full opacity-10" />
          
          {/* Main Icon */}
          <Trophy className={`${currentSize.icon} text-primary-500 relative z-10`} />
          
          {/* Crown Accent */}
          <Crown className="absolute -top-1 -right-1 h-3 w-3 text-secondary-500 z-20" />
        </div>
      </motion.div>
    );
  }

  if (variant === "text-only") {
    return (
      <motion.div
        variants={textVariants}
        initial="initial"
        whileHover="hover"
        className={`${className}`}
      >
        <span className={`font-bold bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent ${currentSize.text}`}>
          Kontestica
        </span>
      </motion.div>
    );
  }

  // Default variant with icon and text
  return (
    <motion.div
      initial="initial"
      whileHover="hover"
      className={`flex items-center space-x-2 ${className}`}
    >
      {/* Icon */}
      <motion.div
        variants={iconVariants as any}
        className={`relative ${currentSize.container}`}
      >
        <div className="relative">
          {/* Background Circle */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full opacity-10" />
          
          {/* Main Icon */}
          <Trophy className={`${currentSize.icon} text-primary-500 relative z-10`} />
          
          {/* Crown Accent */}
          <Crown className="absolute -top-1 -right-1 h-3 w-3 text-secondary-500 z-20" />
        </div>
      </motion.div>

      {/* Text */}
      {showText && (
        <motion.div variants={textVariants}>
          <span className={`font-bold bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent ${currentSize.text}`}>
            Kontestica
          </span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default Logo;
