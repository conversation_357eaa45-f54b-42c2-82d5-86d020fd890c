"use client";

import { motion } from "framer-motion";
import { ArrowR<PERSON>, Sparkles, Trophy, Users, Zap, CheckCircle } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import Button from "@/components/ui/Button";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const CTASection: React.FC = () => {
  const features = [
    "Free 14-day trial",
    "No setup fees",
    "24/7 support",
    "Cancel anytime",
  ];

  const floatingIcons = [
    { icon: Trophy, delay: 0, x: "10%", y: "20%", color: "text-accent-500" },
    { icon: Users, delay: 0.5, x: "85%", y: "30%", color: "text-primary-600" },
    { icon: Zap, delay: 1, x: "15%", y: "75%", color: "text-success-500" },
    { icon: Sparkles, delay: 1.5, x: "80%", y: "80%", color: "text-primary-500" },
  ];

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="100"
    >
      {/* Modern Background Elements */}
      <div className="absolute inset-0">
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-slate-800 dark:to-slate-900" />
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-transparent to-secondary-500/10 dark:from-primary-500/20 dark:to-secondary-500/20" />

        {/* Subtle Pattern Overlay */}
        <div className="absolute inset-0 opacity-20 dark:opacity-30">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(37,99,235,0.2)_1px,transparent_0)] bg-[size:40px_40px] animate-pulse-slow" />
        </div>

        {/* Floating Orbs */}
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-primary-500/15 to-secondary-500/10 dark:from-primary-500/25 dark:to-secondary-500/15 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-secondary-500/15 to-primary-500/10 dark:from-secondary-500/25 dark:to-primary-500/15 rounded-full blur-3xl animate-float" style={{ animationDelay: "2s" }} />

        {/* Floating Elements */}
        {floatingIcons.map((element, index) => (
          <motion.div
            key={index}
            className="absolute hidden lg:block"
            style={{ left: element.x, top: element.y }}
            initial={{ opacity: 0, scale: 0 }}
            animate={{ 
              opacity: 0.6, 
              scale: 1,
              y: [0, -30, 0],
              rotateZ: [0, 360]
            }}
            transition={{
              delay: element.delay,
              duration: 2,
              y: {
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              },
              rotateZ: {
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }
            }}
          >
            <element.icon className={`h-12 w-12 ${element.color} drop-shadow-lg`} />
          </motion.div>
        ))}

        {/* Glow Effects */}
        <div className="absolute top-20 left-20 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-primary-400/20 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="text-center max-w-4xl mx-auto"
        >
          {/* Badge */}
          <motion.div
            variants={staggerItemVariants}
            className="inline-flex items-center space-x-2 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm text-slate-600 dark:text-slate-300 px-4 py-2 rounded-full text-sm font-medium mb-8 border border-slate-200 dark:border-slate-600"
          >
            <Sparkles className="h-4 w-4 text-primary-600" />
            <span>Limited Time Offer</span>
          </motion.div>

          {/* Main Heading */}
          <motion.h2
            variants={staggerItemVariants}
            className="text-5xl lg:text-6xl font-bold font-display bg-gradient-to-r from-primary-600 to-secondary-500 bg-clip-text text-transparent mb-6"
          >
            Ready to Transform Your Contests?
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            variants={staggerItemVariants}
            className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-12 leading-relaxed"
          >
            Join thousands of organizations already using our platform to create
            engaging contests and celebrate their winners.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={staggerItemVariants}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 mb-12"
          >
            <Button
              variant="gradient"
              size="xl"
              rightIcon={<ArrowRight className="h-6 w-6" />}
              glow={true}
              className="px-12 py-5 text-xl font-semibold"
              motionProps={{
                whileHover: { scale: 1.05, y: -3 },
                whileTap: { scale: 0.95 },
              }}
            >
              Start Free Trial
            </Button>

            <Button
              variant="outline"
              size="xl"
              className="px-12 py-5 text-xl font-semibold"
              motionProps={{
                whileHover: { scale: 1.05, y: -3 },
                whileTap: { scale: 0.95 },
              }}
            >
              Schedule Demo
            </Button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            variants={staggerItemVariants}
            className="text-center"
          >
            <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
              Trusted by leading organizations worldwide
            </p>

            {/* Features List */}
            <div className="flex flex-wrap justify-center items-center gap-6 opacity-80">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-slate-600 dark:text-slate-300 text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary-500/30 to-transparent" />
    </AnimatedSection>
  );
};

export default CTASection;
