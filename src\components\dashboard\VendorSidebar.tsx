"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";
import { 
  LayoutDashboard, 
  Trophy, 
  Users, 
  BarChart3, 
  Settings, 
  CreditCard,
  Plus,
  Eye,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Calendar,
  DollarSign,
  UserCheck,
  Award,
  Target
} from "lucide-react";

interface SidebarItem {
  name: string;
  href: string;
  icon: any;
  badge?: string;
  children?: SidebarItem[];
}

const sidebarItems: SidebarItem[] = [
  {
    name: "Dashboard",
    href: "/vendor",
    icon: LayoutDashboard,
  },
  {
    name: "Contest Management",
    href: "/vendor/contests",
    icon: Trophy,
    badge: "5",
    children: [
      { name: "All Contests", href: "/vendor/contests", icon: Trophy },
      { name: "Create Contest", href: "/vendor/contests/create", icon: Plus },
      { name: "Active Contests", href: "/vendor/contests/active", icon: Eye },
      { name: "Draft Contests", href: "/vendor/contests/drafts", icon: Calendar },
    ],
  },
  {
    name: "Participants",
    href: "/vendor/participants",
    icon: Users,
    children: [
      { name: "All Participants", href: "/vendor/participants", icon: Users },
      { name: "Contestants", href: "/vendor/participants/contestants", icon: UserCheck },
      { name: "Voters", href: "/vendor/participants/voters", icon: Target },
    ],
  },
  {
    name: "Analytics",
    href: "/vendor/analytics",
    icon: BarChart3,
    children: [
      { name: "Contest Performance", href: "/vendor/analytics", icon: BarChart3 },
      { name: "Engagement Metrics", href: "/vendor/analytics/engagement", icon: Users },
      { name: "Revenue Analytics", href: "/vendor/analytics/revenue", icon: DollarSign },
    ],
  },
  {
    name: "Payments",
    href: "/vendor/payments",
    icon: CreditCard,
    children: [
      { name: "Payment Overview", href: "/vendor/payments", icon: CreditCard },
      { name: "Transaction History", href: "/vendor/payments/transactions", icon: DollarSign },
      { name: "Billing Settings", href: "/vendor/payments/billing", icon: Settings },
    ],
  },
  {
    name: "Awards & Prizes",
    href: "/vendor/awards",
    icon: Award,
  },
  {
    name: "Settings",
    href: "/vendor/settings",
    icon: Settings,
  },
];

export default function VendorSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const isActive = (href: string) => {
    if (href === "/vendor") {
      return pathname === "/vendor";
    }
    return pathname.startsWith(href);
  };

  const isParentActive = (item: SidebarItem) => {
    if (isActive(item.href)) return true;
    if (item.children) {
      return item.children.some(child => isActive(child.href));
    }
    return false;
  };

  return (
    <motion.div
      initial={false}
      animate={{ width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col"
    >
      {/* Logo and Toggle */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <AnimatePresence mode="wait">
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-3"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <Trophy className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                Vendor Portal
              </span>
            </motion.div>
          )}
        </AnimatePresence>
        
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          {isCollapsed ? (
            <ChevronRight className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          ) : (
            <ChevronLeft className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        <div className="space-y-1 px-3">
          {sidebarItems.map((item) => (
            <div key={item.name}>
              {/* Main Item */}
              <div className="relative">
                {item.children ? (
                  <button
                    onClick={() => toggleExpanded(item.name)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      isParentActive(item)
                        ? "bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    }`}
                  >
                    <item.icon className="h-5 w-5 flex-shrink-0" />
                    <AnimatePresence mode="wait">
                      {!isCollapsed && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.2 }}
                          className="ml-3 flex-1 flex items-center justify-between"
                        >
                          <span>{item.name}</span>
                          <div className="flex items-center space-x-2">
                            {item.badge && (
                              <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-xs px-2 py-1 rounded-full">
                                {item.badge}
                              </span>
                            )}
                            <ChevronDown 
                              className={`h-4 w-4 transition-transform ${
                                expandedItems.includes(item.name) ? "rotate-180" : ""
                              }`} 
                            />
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </button>
                ) : (
                  <Link
                    href={item.href}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                      isActive(item.href)
                        ? "bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    }`}
                  >
                    <item.icon className="h-5 w-5 flex-shrink-0" />
                    <AnimatePresence mode="wait">
                      {!isCollapsed && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.2 }}
                          className="ml-3 flex-1 flex items-center justify-between"
                        >
                          <span>{item.name}</span>
                          {item.badge && (
                            <span className="bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 text-xs px-2 py-1 rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Link>
                )}
              </div>

              {/* Submenu */}
              <AnimatePresence>
                {item.children && expandedItems.includes(item.name) && !isCollapsed && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="ml-6 mt-1 space-y-1"
                  >
                    {item.children.map((child) => (
                      <Link
                        key={child.name}
                        href={child.href}
                        className={`flex items-center px-3 py-2 text-sm rounded-lg transition-colors ${
                          isActive(child.href)
                            ? "bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300"
                            : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                        }`}
                      >
                        <child.icon className="h-4 w-4 flex-shrink-0" />
                        <span className="ml-3">{child.name}</span>
                      </Link>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </nav>

      {/* User Section */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">V</span>
          </div>
          <AnimatePresence mode="wait">
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="ml-3"
              >
                <p className="text-sm font-medium text-gray-900 dark:text-white">TechCorp</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Vendor Account</p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
}
