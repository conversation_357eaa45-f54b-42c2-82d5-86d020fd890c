"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  Save,
  Upload,
  Calendar,
  DollarSign,
  Users,
  Settings
} from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { ContestsService, CreateContestData } from "@/lib/api/contests";
import { handleApiError } from "@/lib/api";
import { useToast } from "@/components/ui/Toast";

interface ContestFormData {
  title: string;
  description: string;
  votePrice: number;
  registrationFee: number;
  registrationStartDate: string;
  registrationCloseDate: string;
  votingStartDate: string;
  votingCloseDate: string;
  startsAt: string;
  endsAt: string;
  requireApproval: boolean;
  numberOfWinners: number;
  banner: File | null;
}

export default function CreateContestPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<ContestFormData>>({});
  const { success, error } = useToast();
  const [formData, setFormData] = useState<ContestFormData>({
    title: "",
    description: "",
    votePrice: 200,
    registrationFee: 1000,
    registrationStartDate: "",
    registrationCloseDate: "",
    votingStartDate: "",
    votingCloseDate: "",
    startsAt: "",
    endsAt: "",
    requireApproval: false,
    numberOfWinners: 1,
    banner: null,
  });

  const handleInputChange = (field: keyof ContestFormData, value: string | number | boolean | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ContestFormData> = {};

    if (!formData.title.trim()) newErrors.title = "Title is required";
    if (!formData.description.trim()) newErrors.description = "Description is required";
    if (formData.votePrice <= 0) newErrors.votePrice = "Vote price must be greater than 0";
    if (formData.registrationFee <= 0) newErrors.registrationFee = "Registration fee must be greater than 0";
    if (!formData.registrationStartDate) newErrors.registrationStartDate = "Registration start date is required";
    if (!formData.registrationCloseDate) newErrors.registrationCloseDate = "Registration close date is required";
    if (!formData.votingStartDate) newErrors.votingStartDate = "Voting start date is required";
    if (!formData.votingCloseDate) newErrors.votingCloseDate = "Voting close date is required";
    if (!formData.startsAt) newErrors.startsAt = "Contest start date is required";
    if (!formData.endsAt) newErrors.endsAt = "Contest end date is required";
    if (formData.numberOfWinners <= 0) newErrors.numberOfWinners = "Number of winners must be at least 1";

    // Date validations
    if (formData.registrationStartDate && formData.registrationCloseDate) {
      if (new Date(formData.registrationStartDate) >= new Date(formData.registrationCloseDate)) {
        newErrors.registrationCloseDate = "Registration close date must be after start date";
      }
    }

    if (formData.votingStartDate && formData.votingCloseDate) {
      if (new Date(formData.votingStartDate) >= new Date(formData.votingCloseDate)) {
        newErrors.votingCloseDate = "Voting close date must be after start date";
      }
    }

    if (formData.startsAt && formData.endsAt) {
      if (new Date(formData.startsAt) >= new Date(formData.endsAt)) {
        newErrors.endsAt = "Contest end date must be after start date";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const createData: CreateContestData = {
        title: formData.title,
        description: formData.description,
        votePrice: formData.votePrice,
        registrationFee: formData.registrationFee,
        registrationStartDate: formData.registrationStartDate,
        registrationCloseDate: formData.registrationCloseDate,
        votingStartDate: formData.votingStartDate,
        votingCloseDate: formData.votingCloseDate,
        startsAt: formData.startsAt,
        endsAt: formData.endsAt,
        requireApproval: formData.requireApproval,
        numberOfWinners: formData.numberOfWinners,
        banner: formData.banner
      };

      const response = await ContestsService.createContest(createData);

      if (response.success) {
        success("Contest created successfully!", "Your contest has been created and saved as a draft.");
        // Redirect to the new contest page
        router.push(`/vendor/contests/${response.contest._id}`);
      } else {
        error("Failed to create contest", response.message || "Please check your form and try again.");
      }
    } catch (err: any) {
      console.error("Error creating contest:", err);
      error("Failed to create contest", handleApiError(err));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href="/vendor/contests">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Contests
            </Button>
          </Link>
        </div>

        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Create New Contest
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Set up your people-based contest with our step-by-step wizard
          </p>
        </div>

        {/* Form */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card className="p-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information */}
                <div className="space-y-6">
                  <div className="flex items-center gap-3 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-lg">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Basic Information</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Contest title, description and category</p>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <Input
                      label="Contest Title"
                      placeholder="e.g., Face of Lagos 2024"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      error={errors.title}
                      required
                      className="text-lg"
                    />

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Contest Description
                      </label>
                      <textarea
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                        rows={4}
                        placeholder="Describe your contest, its purpose, and what makes it special..."
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                      />
                      {errors.description && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Pricing & Settings */}
                <div className="space-y-6">
                  <div className="flex items-center gap-3 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                      <DollarSign className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Pricing & Settings</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Set vote prices and registration fees</p>
                    </div>
                  </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Input
                  label="Vote Price (₦)"
                  type="number"
                  placeholder="200"
                  value={formData.votePrice}
                  onChange={(e) => handleInputChange('votePrice', parseInt(e.target.value) || 0)}
                  error={errors.votePrice}
                  required
                />
                <Input
                  label="Registration Fee (₦)"
                  type="number"
                  placeholder="1000"
                  value={formData.registrationFee}
                  onChange={(e) => handleInputChange('registrationFee', parseInt(e.target.value) || 0)}
                  error={errors.registrationFee}
                  required
                />
                <Input
                  label="Number of Winners"
                  type="number"
                  placeholder="1"
                  value={formData.numberOfWinners}
                  onChange={(e) => handleInputChange('numberOfWinners', parseInt(e.target.value) || 1)}
                  error={errors.numberOfWinners}
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireApproval"
                  checked={formData.requireApproval}
                  onChange={(e) => handleInputChange('requireApproval', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="requireApproval" className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                  Require approval for contestant registration
                </label>
              </div>
            </div>

            {/* Timeline */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 pb-4 border-b border-gray-200 dark:border-gray-700">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                  <Calendar className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Contest Timeline</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Set registration, voting, and contest dates</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Registration Start Date"
                  type="datetime-local"
                  value={formData.registrationStartDate}
                  onChange={(e) => handleInputChange('registrationStartDate', e.target.value)}
                  error={errors.registrationStartDate}
                  required
                />
                <Input
                  label="Registration Close Date"
                  type="datetime-local"
                  value={formData.registrationCloseDate}
                  onChange={(e) => handleInputChange('registrationCloseDate', e.target.value)}
                  error={errors.registrationCloseDate}
                  required
                />
                <Input
                  label="Voting Start Date"
                  type="datetime-local"
                  value={formData.votingStartDate}
                  onChange={(e) => handleInputChange('votingStartDate', e.target.value)}
                  error={errors.votingStartDate}
                  required
                />
                <Input
                  label="Voting Close Date"
                  type="datetime-local"
                  value={formData.votingCloseDate}
                  onChange={(e) => handleInputChange('votingCloseDate', e.target.value)}
                  error={errors.votingCloseDate}
                  required
                />
                <Input
                  label="Contest Start Date"
                  type="datetime-local"
                  value={formData.startsAt}
                  onChange={(e) => handleInputChange('startsAt', e.target.value)}
                  error={errors.startsAt}
                  required
                />
                <Input
                  label="Contest End Date"
                  type="datetime-local"
                  value={formData.endsAt}
                  onChange={(e) => handleInputChange('endsAt', e.target.value)}
                  error={errors.endsAt}
                  required
                />
              </div>
            </div>

            {/* Banner Upload */}
            <div className="space-y-6">
              <div className="flex items-center gap-3 pb-4 border-b border-gray-200 dark:border-gray-700">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                  <Upload className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Contest Banner</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Upload a banner image for your contest</p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Banner Image (Optional)
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500">
                  <div className="space-y-1 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="flex text-sm text-gray-600 dark:text-gray-400">
                      <label htmlFor="banner-upload" className="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                        <span>Upload a file</span>
                        <input
                          id="banner-upload"
                          name="banner-upload"
                          type="file"
                          accept="image/*"
                          className="sr-only"
                          onChange={(e) => {
                            const file = e.target.files?.[0] || null;
                            handleInputChange('banner', file);
                          }}
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      PNG, JPG, GIF up to 10MB
                    </p>
                  </div>
                </div>
                {formData.banner && (
                  <p className="mt-2 text-sm text-green-600 dark:text-green-400">
                    Selected: {formData.banner.name}
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="submit"
                variant="gradient"
                size="lg"
                disabled={isSubmitting}
                className="min-w-[200px]"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Contest...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Create Contest
                  </>
                )}
              </Button>
            </div>
          </form>
        </Card>
      </div>

      {/* Sidebar */}
      <div className="space-y-6">
        {/* Tips Card */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Settings className="w-5 h-5 text-primary-500" />
            Contest Tips
          </h3>
          <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Choose a clear, descriptive title that captures the essence of your contest</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Set reasonable registration and vote prices to encourage participation</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Allow enough time between registration and voting periods</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Upload an attractive banner to make your contest stand out</p>
            </div>
          </div>
        </Card>

        {/* Preview Card */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Users className="w-5 h-5 text-green-500" />
            Contest Preview
          </h3>
          <div className="space-y-3">
            <div>
              <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Title</p>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {formData.title || "Your contest title"}
              </p>
            </div>
            <div>
              <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Vote Price</p>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                ₦{formData.votePrice.toLocaleString()}
              </p>
            </div>
            <div>
              <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Registration Fee</p>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                ₦{formData.registrationFee.toLocaleString()}
              </p>
            </div>
            <div>
              <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Winners</p>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                {formData.numberOfWinners} {formData.numberOfWinners === 1 ? 'winner' : 'winners'}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</div>
);

