"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ArrowLeft,
  ArrowRight,
  Trophy,
  Users,
  DollarSign,
  Calendar,
  FileText,
  Settings,
  CheckCircle,
  Upload,
  Plus,
  X
} from "lucide-react";
import { useRouter } from "next/navigation";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { ContestsService } from "@/lib/api/contests";

interface ContestFormData {
  // Basic Information
  title: string;
  description: string;
  category: string;
  organizer: string;
  
  // Contest Details
  startDate: string;
  endDate: string;
  prize: string;
  entryFee: string;
  maxContestants: string;
  
  // Requirements
  votePrice: string;
  requirements: string[];
  
  // Rules and Criteria
  rules: string[];
  judgingCriteria: string[];
  
  // Media
  bannerImage: File | null;
}

const initialFormData: ContestFormData = {
  title: "",
  description: "",
  category: "",
  organizer: "",
  startDate: "",
  endDate: "",
  prize: "",
  entryFee: "",
  maxContestants: "",
  votePrice: "",
  requirements: [],
  rules: [],
  judgingCriteria: [],
  bannerImage: null
};

const categories = [
  "Beauty & Talent",
  "Male Pageant", 
  "Student Pageant",
  "Male Beauty",
  "Business & Beauty",
  "Comedy & Entertainment",
  "Fashion & Style",
  "Fitness & Health",
  "Cultural & Traditional"
];

const steps = [
  {
    id: 1,
    title: "Basic Information",
    description: "Contest title, description, and category",
    icon: FileText
  },
  {
    id: 2,
    title: "Contest Details",
    description: "Timeline, prizes, and participation limits",
    icon: Settings
  },
  {
    id: 3,
    title: "Requirements",
    description: "Eligibility criteria and contestant requirements",
    icon: Users
  },
  {
    id: 4,
    title: "Rules & Criteria",
    description: "Contest rules and judging criteria",
    icon: Trophy
  },
  {
    id: 5,
    title: "Review & Publish",
    description: "Review all details and publish your contest",
    icon: CheckCircle
  }
];

export default function CreateContestPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<ContestFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateFormData = (field: keyof ContestFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addToArray = (field: 'requirements' | 'rules' | 'judgingCriteria', value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...prev[field], value.trim()]
      }));
    }
  };

  const removeFromArray = (field: 'requirements' | 'rules' | 'judgingCriteria', index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Prepare contest data for API
      const contestData = {
        title: formData.title,
        description: formData.description,
        votePrice: parseFloat(formData.votePrice) || 0,
        registrationFee: parseFloat(formData.entryFee) || 0,
        registrationStartDate: new Date().toISOString(), // Start registration immediately
        registrationCloseDate: new Date(formData.startDate).toISOString(),
        votingStartDate: new Date(formData.startDate).toISOString(),
        votingCloseDate: new Date(formData.endDate).toISOString(),
        startsAt: new Date(formData.startDate).toISOString(),
        endsAt: new Date(formData.endDate).toISOString(),
        requireApproval: false, // You can add this as a form field if needed
        numberOfWinners: 1, // You can add this as a form field if needed
        banner: formData.bannerImage,
      };

      const response = await ContestsService.createContest(contestData);

      if (response.success) {
        router.push('/vendor?created=true');
      } else {
        throw new Error(response.message || 'Failed to create contest');
      }
    } catch (error: any) {
      console.error('Error creating contest:', error);
      alert('Failed to create contest. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.title && formData.description && formData.category);
      case 2:
        return !!(formData.startDate && formData.endDate && formData.prize);
      case 3:
        return formData.requirements.length > 0;
      case 4:
        return formData.rules.length > 0;
      case 5:
        return true;
      default:
        return false;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          leftIcon={<ArrowLeft className="w-4 h-4" />}
          onClick={() => router.push('/vendor')}
          className="mb-4"
        >
          Back to Dashboard
        </Button>
        
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Create New Contest
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Set up your people-based contest with our step-by-step wizard
        </p>
      </div>

      {/* Progress Steps */}
      <Card className="mb-8">
        <div className="p-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                  currentStep === step.id 
                    ? 'border-primary-500 bg-primary-500 text-white' 
                    : currentStep > step.id
                    ? 'border-green-500 bg-green-500 text-white'
                    : 'border-gray-300 dark:border-gray-600 text-gray-400'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <step.icon className="w-5 h-5" />
                  )}
                </div>
                
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 transition-all duration-200 ${
                    currentStep > step.id ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'
                  }`} />
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {steps[currentStep - 1].title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {steps[currentStep - 1].description}
            </p>
          </div>
        </div>
      </Card>

      {/* Form Content */}
      <Card>
        <div className="p-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStep === 1 && (
                <BasicInformationStep 
                  formData={formData} 
                  updateFormData={updateFormData}
                  categories={categories}
                />
              )}
              
              {currentStep === 2 && (
                <ContestDetailsStep 
                  formData={formData} 
                  updateFormData={updateFormData}
                />
              )}
              
              {currentStep === 3 && (
                <RequirementsStep 
                  formData={formData} 
                  updateFormData={updateFormData}
                  addToArray={addToArray}
                  removeFromArray={removeFromArray}
                />
              )}
              
              {currentStep === 4 && (
                <RulesAndCriteriaStep 
                  formData={formData} 
                  updateFormData={updateFormData}
                  addToArray={addToArray}
                  removeFromArray={removeFromArray}
                />
              )}
              
              {currentStep === 5 && (
                <ReviewStep 
                  formData={formData}
                />
              )}
            </motion.div>
          </AnimatePresence>
        </div>
        
        {/* Navigation */}
        <div className="px-8 py-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
          <Button
            variant="outline"
            leftIcon={<ArrowLeft className="w-4 h-4" />}
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            Previous
          </Button>
          
          {currentStep < steps.length ? (
            <Button
              variant="gradient"
              rightIcon={<ArrowRight className="w-4 h-4" />}
              onClick={nextStep}
              disabled={!isStepValid(currentStep)}
              glow={isStepValid(currentStep)}
            >
              Next Step
            </Button>
          ) : (
            <Button
              variant="gradient"
              leftIcon={<Trophy className="w-4 h-4" />}
              onClick={handleSubmit}
              isLoading={isSubmitting}
              loadingText="Creating Contest..."
              glow={true}
            >
              Create Contest
            </Button>
          )}
        </div>
      </Card>
    </div>
  );
}

// Step Components
function BasicInformationStep({
  formData,
  updateFormData,
  categories
}: {
  formData: ContestFormData;
  updateFormData: (field: keyof ContestFormData, value: any) => void;
  categories: string[];
}) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Basic Contest Information
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Provide the essential details about your contest
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <Input
            label="Contest Title"
            placeholder="e.g., Face of Lagos 2024"
            value={formData.title}
            onChange={(e) => updateFormData('title', e.target.value)}
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Category
          </label>
          <select
            value={formData.category}
            onChange={(e) => updateFormData('category', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            required
          >
            <option value="">Select a category</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        <div>
          <Input
            label="Organizer Name"
            placeholder="Your organization name"
            value={formData.organizer}
            onChange={(e) => updateFormData('organizer', e.target.value)}
            required
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Contest Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => updateFormData('description', e.target.value)}
            placeholder="Describe your contest, its purpose, and what makes it special..."
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            required
          />
        </div>
      </div>
    </div>
  );
}

function ContestDetailsStep({
  formData,
  updateFormData
}: {
  formData: ContestFormData;
  updateFormData: (field: keyof ContestFormData, value: any) => void;
}) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Contest Timeline & Details
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Set up the timeline, prizes, and participation details
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Input
            label="Start Date"
            type="date"
            value={formData.startDate}
            onChange={(e) => updateFormData('startDate', e.target.value)}
            required
          />
        </div>

        <div>
          <Input
            label="End Date"
            type="date"
            value={formData.endDate}
            onChange={(e) => updateFormData('endDate', e.target.value)}
            required
          />
        </div>

        <div>
          <Input
            label="Prize Amount (₦)"
            placeholder="e.g., 1,000,000"
            value={formData.prize}
            onChange={(e) => updateFormData('prize', e.target.value)}
            required
          />
        </div>

        <div>
          <Input
            label="Entry Fee (₦)"
            placeholder="e.g., 50,000 (leave empty for free)"
            value={formData.entryFee}
            onChange={(e) => updateFormData('entryFee', e.target.value)}
          />
        </div>

        <div>
          <Input
            label="Maximum Contestants"
            type="number"
            placeholder="e.g., 100"
            value={formData.maxContestants}
            onChange={(e) => updateFormData('maxContestants', e.target.value)}
          />
        </div>

        <div>
          <Input
            label="Vote Price (₦)"
            placeholder="e.g., 100 (cost per vote)"
            value={formData.votePrice}
            onChange={(e) => updateFormData('votePrice', e.target.value)}
            required
          />
        </div>
      </div>
    </div>
  );
}

function RequirementsStep({
  formData,
  updateFormData,
  addToArray,
  removeFromArray
}: {
  formData: ContestFormData;
  updateFormData: (field: keyof ContestFormData, value: any) => void;
  addToArray: (field: 'requirements' | 'rules' | 'judgingCriteria', value: string) => void;
  removeFromArray: (field: 'requirements' | 'rules' | 'judgingCriteria', index: number) => void;
}) {
  const [newRequirement, setNewRequirement] = useState("");

  const handleAddRequirement = () => {
    addToArray('requirements', newRequirement);
    setNewRequirement("");
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Contestant Requirements
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Define the eligibility criteria for contestants
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex gap-2">
          <Input
            placeholder="Add a requirement (e.g., Must be a Nigerian citizen)"
            value={newRequirement}
            onChange={(e) => setNewRequirement(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddRequirement()}
          />
          <Button
            variant="outline"
            leftIcon={<Plus className="w-4 h-4" />}
            onClick={handleAddRequirement}
            disabled={!newRequirement.trim()}
          >
            Add
          </Button>
        </div>

        {formData.requirements.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 dark:text-gray-100">Requirements:</h4>
            {formData.requirements.map((requirement, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <span className="text-gray-700 dark:text-gray-300">{requirement}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<X className="w-4 h-4" />}
                  onClick={() => removeFromArray('requirements', index)}
                  className="text-red-500 hover:text-red-700"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}

        {formData.requirements.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No requirements added yet. Add at least one requirement to continue.
          </div>
        )}
      </div>
    </div>
  );
}

function RulesAndCriteriaStep({
  formData,
  updateFormData,
  addToArray,
  removeFromArray
}: {
  formData: ContestFormData;
  updateFormData: (field: keyof ContestFormData, value: any) => void;
  addToArray: (field: 'requirements' | 'rules' | 'judgingCriteria', value: string) => void;
  removeFromArray: (field: 'requirements' | 'rules' | 'judgingCriteria', index: number) => void;
}) {
  const [newRule, setNewRule] = useState("");
  const [newCriteria, setNewCriteria] = useState("");

  const handleAddRule = () => {
    addToArray('rules', newRule);
    setNewRule("");
  };

  const handleAddCriteria = () => {
    addToArray('judgingCriteria', newCriteria);
    setNewCriteria("");
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Contest Rules & Judging Criteria
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Set the rules and criteria for your contest
        </p>
      </div>

      {/* Contest Rules */}
      <div className="space-y-4">
        <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Contest Rules</h4>

        <div className="flex gap-2">
          <Input
            placeholder="Add a contest rule (e.g., Contestants must be available for the full duration)"
            value={newRule}
            onChange={(e) => setNewRule(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddRule()}
          />
          <Button
            variant="outline"
            leftIcon={<Plus className="w-4 h-4" />}
            onClick={handleAddRule}
            disabled={!newRule.trim()}
          >
            Add
          </Button>
        </div>

        {formData.rules.length > 0 && (
          <div className="space-y-2">
            {formData.rules.map((rule, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <span className="text-gray-700 dark:text-gray-300">{rule}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<X className="w-4 h-4" />}
                  onClick={() => removeFromArray('rules', index)}
                  className="text-red-500 hover:text-red-700"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Judging Criteria */}
      <div className="space-y-4">
        <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Judging Criteria (Optional)</h4>

        <div className="flex gap-2">
          <Input
            placeholder="Add judging criteria (e.g., Beauty and Poise - 25%)"
            value={newCriteria}
            onChange={(e) => setNewCriteria(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddCriteria()}
          />
          <Button
            variant="outline"
            leftIcon={<Plus className="w-4 h-4" />}
            onClick={handleAddCriteria}
            disabled={!newCriteria.trim()}
          >
            Add
          </Button>
        </div>

        {formData.judgingCriteria.length > 0 && (
          <div className="space-y-2">
            {formData.judgingCriteria.map((criteria, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <span className="text-gray-700 dark:text-gray-300">{criteria}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<X className="w-4 h-4" />}
                  onClick={() => removeFromArray('judgingCriteria', index)}
                  className="text-red-500 hover:text-red-700"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {formData.rules.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          Add at least one contest rule to continue.
        </div>
      )}
    </div>
  );
}

function ReviewStep({ formData }: { formData: ContestFormData }) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Review Your Contest
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Please review all the details before creating your contest
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card variant="elevated">
          <div className="p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2 text-primary-500" />
              Basic Information
            </h4>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Title:</span>
                <p className="text-gray-900 dark:text-gray-100">{formData.title}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Category:</span>
                <p className="text-gray-900 dark:text-gray-100">{formData.category}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Organizer:</span>
                <p className="text-gray-900 dark:text-gray-100">{formData.organizer}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Description:</span>
                <p className="text-gray-900 dark:text-gray-100 text-sm">{formData.description}</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Contest Details */}
        <Card variant="elevated">
          <div className="p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <Settings className="w-5 h-5 mr-2 text-primary-500" />
              Contest Details
            </h4>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Duration:</span>
                <p className="text-gray-900 dark:text-gray-100">{formData.startDate} to {formData.endDate}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Prize:</span>
                <p className="text-gray-900 dark:text-gray-100">₦{formData.prize}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Entry Fee:</span>
                <p className="text-gray-900 dark:text-gray-100">
                  {formData.entryFee ? `₦${formData.entryFee}` : 'Free'}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Max Contestants:</span>
                <p className="text-gray-900 dark:text-gray-100">
                  {formData.maxContestants || 'Unlimited'}
                </p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">Vote Price:</span>
                <p className="text-gray-900 dark:text-gray-100">
                  {formData.votePrice ? `₦${formData.votePrice} per vote` : 'Free voting'}
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Requirements */}
        <Card variant="elevated">
          <div className="p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <Users className="w-5 h-5 mr-2 text-primary-500" />
              Requirements ({formData.requirements.length})
            </h4>
            <div className="space-y-2">
              {formData.requirements.map((requirement, index) => (
                <div key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-sm text-gray-700 dark:text-gray-300">{requirement}</span>
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Rules */}
        <Card variant="elevated">
          <div className="p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <Trophy className="w-5 h-5 mr-2 text-primary-500" />
              Rules ({formData.rules.length})
            </h4>
            <div className="space-y-2">
              {formData.rules.map((rule, index) => (
                <div key={index} className="flex items-start">
                  <span className="flex-shrink-0 w-5 h-5 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center text-xs font-medium mr-2 mt-0.5">
                    {index + 1}
                  </span>
                  <span className="text-sm text-gray-700 dark:text-gray-300">{rule}</span>
                </div>
              ))}
            </div>
          </div>
        </Card>

        {/* Judging Criteria */}
        {formData.judgingCriteria.length > 0 && (
          <Card variant="elevated" className="lg:col-span-2">
            <div className="p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-primary-500" />
                Judging Criteria ({formData.judgingCriteria.length})
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {formData.judgingCriteria.map((criteria, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <span className="text-sm text-gray-700 dark:text-gray-300">{criteria}</span>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-start">
          <CheckCircle className="w-5 h-5 text-blue-500 mr-3 mt-0.5" />
          <div>
            <h5 className="font-medium text-blue-900 dark:text-blue-100">Ready to Create</h5>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
              Your contest will be created as a draft and you can publish it when ready.
              You can always edit these details later from your dashboard.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
