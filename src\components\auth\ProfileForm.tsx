"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { User, Mail, Building, Phone, MapPin, Camera, Save } from "lucide-react";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Card from "@/components/ui/Card";

interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  organizationName?: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  website?: string;
  avatar?: File | null;
}

interface ProfileFormProps {
  userRole: "admin" | "vendor" | "contestant" | "voter";
  initialData?: Partial<ProfileFormData>;
  onSubmit: (data: ProfileFormData) => Promise<void>;
  isLoading?: boolean;
}

export default function ProfileForm({ 
  userRole, 
  initialData = {}, 
  onSubmit, 
  isLoading = false 
}: ProfileFormProps) {
  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: initialData.firstName || "",
    lastName: initialData.lastName || "",
    email: initialData.email || "",
    phone: initialData.phone || "",
    organizationName: initialData.organizationName || "",
    address: initialData.address || "",
    city: initialData.city || "",
    country: initialData.country || "",
    bio: initialData.bio || "",
    website: initialData.website || "",
    avatar: null,
  });
  
  const [errors, setErrors] = useState<Partial<ProfileFormData>>({});
  const [avatarPreview, setAvatarPreview] = useState<string>("");

  const handleInputChange = (field: keyof ProfileFormData, value: string | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Handle avatar preview
    if (field === "avatar" && value instanceof File) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(value);
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileFormData> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
    }

    if (userRole === "vendor" && !formData.organizationName?.trim()) {
      newErrors.organizationName = "Organization name is required";
    }

    if (formData.website && !/^https?:\/\/.+/.test(formData.website)) {
      newErrors.website = "Please enter a valid website URL";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Profile update error:", error);
    }
  };

  const getRoleSpecificFields = () => {
    switch (userRole) {
      case "vendor":
        return (
          <>
            <Input
              type="text"
              placeholder="Organization name"
              value={formData.organizationName}
              onChange={(e) => handleInputChange("organizationName", e.target.value)}
              leftIcon={<Building className="h-5 w-5" />}
              error={errors.organizationName}
              label="Organization Name"
              required
            />
            <Input
              type="url"
              placeholder="https://yourwebsite.com"
              value={formData.website}
              onChange={(e) => handleInputChange("website", e.target.value)}
              leftIcon={<Building className="h-5 w-5" />}
              error={errors.website}
              label="Website"
            />
          </>
        );
      case "contestant":
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Bio
            </label>
            <textarea
              value={formData.bio}
              onChange={(e) => handleInputChange("bio", e.target.value)}
              placeholder="Tell us about yourself..."
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white resize-none"
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="p-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Profile Information
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Update your profile information and preferences
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Avatar Upload */}
          <div className="flex items-center space-x-6">
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center overflow-hidden">
                {avatarPreview ? (
                  <img src={avatarPreview} alt="Avatar" className="w-full h-full object-cover" />
                ) : (
                  <User className="h-12 w-12 text-white" />
                )}
              </div>
              <label className="absolute bottom-0 right-0 w-8 h-8 bg-white dark:bg-gray-800 rounded-full border-2 border-gray-200 dark:border-gray-600 flex items-center justify-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Camera className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleInputChange("avatar", e.target.files?.[0] || null)}
                  className="sr-only"
                />
              </label>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Profile Photo
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Upload a photo to personalize your profile
              </p>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              type="text"
              placeholder="First name"
              value={formData.firstName}
              onChange={(e) => handleInputChange("firstName", e.target.value)}
              leftIcon={<User className="h-5 w-5" />}
              error={errors.firstName}
              label="First Name"
              required
            />
            <Input
              type="text"
              placeholder="Last name"
              value={formData.lastName}
              onChange={(e) => handleInputChange("lastName", e.target.value)}
              leftIcon={<User className="h-5 w-5" />}
              error={errors.lastName}
              label="Last Name"
              required
            />
          </div>

          <Input
            type="email"
            placeholder="Email address"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            leftIcon={<Mail className="h-5 w-5" />}
            error={errors.email}
            label="Email Address"
            required
          />

          <Input
            type="tel"
            placeholder="Phone number"
            value={formData.phone}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            leftIcon={<Phone className="h-5 w-5" />}
            error={errors.phone}
            label="Phone Number"
          />

          {/* Role-specific fields */}
          {getRoleSpecificFields()}

          {/* Address Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Address Information
            </h3>
            
            <Input
              type="text"
              placeholder="Street address"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              leftIcon={<MapPin className="h-5 w-5" />}
              error={errors.address}
              label="Address"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                type="text"
                placeholder="City"
                value={formData.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
                leftIcon={<MapPin className="h-5 w-5" />}
                error={errors.city}
                label="City"
              />
              <Input
                type="text"
                placeholder="Country"
                value={formData.country}
                onChange={(e) => handleInputChange("country", e.target.value)}
                leftIcon={<MapPin className="h-5 w-5" />}
                error={errors.country}
                label="Country"
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-6">
            <Button
              type="submit"
              variant="gradient"
              size="lg"
              isLoading={isLoading}
              loadingText="Saving..."
              leftIcon={<Save className="h-5 w-5" />}
            >
              Save Changes
            </Button>
          </div>
        </form>
      </motion.div>
    </Card>
  );
}
