// API Services Export
export { AuthService } from './auth';
export { ContestsService } from './contests';
export { ContestantsService } from './contestants';
export { VotingService } from './voting';

// Re-export types
export type {
  RegisterData,
  LoginData,
  LoginResponse,
  RegisterResponse,
  VerifyEmailData,
} from './auth';

export type {
  Contest,
  CreateContestData,
  UpdateContestData,
  ContestResponse,
  ContestsListResponse,
} from './contests';

export type {
  Contestant,
  CreateContestantData,
  UpdateContestantData,
  ContestantResponse,
  ContestantsListResponse,
} from './contestants';

export type {
  VoteData,
  VoteResponse,
  VoteRecord,
  VotingStatsResponse,
  ContestantVotesResponse,
} from './voting';

// Re-export base API utilities
export {
  apiClient,
  getAuthToken,
  setAuthToken,
  removeAuthToken,
  handleApiError,
  validateApiResponse,
} from '../api';

export type {
  ApiResponse,
  ApiError,
  ApiConfig,
} from '../api';

// Convenience API object
export const API = {
  auth: AuthService,
  contests: ContestsService,
  contestants: ContestantsService,
  voting: VotingService,
};

export default API;
