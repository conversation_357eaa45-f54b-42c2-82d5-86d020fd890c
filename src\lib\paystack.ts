// Paystack Integration Utilities
export interface PaystackConfig {
  publicKey: string;
  secretKey: string;
  baseUrl: string;
}

export interface PaymentData {
  email: string;
  amount: number; // Amount in kobo (multiply by 100)
  currency: string;
  reference: string;
  callback_url?: string;
  metadata?: {
    contest_id?: string;
    contestant_id?: string;
    payment_type: 'entry_fee' | 'prize_distribution' | 'vendor_payout';
    [key: string]: any;
  };
}

export interface PaymentVerificationResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    domain: string;
    status: string;
    reference: string;
    amount: number;
    message: string | null;
    gateway_response: string;
    paid_at: string;
    created_at: string;
    channel: string;
    currency: string;
    ip_address: string;
    metadata: any;
    fees_breakdown: any;
    log: any;
    fees: number;
    fees_split: any;
    authorization: {
      authorization_code: string;
      bin: string;
      last4: string;
      exp_month: string;
      exp_year: string;
      channel: string;
      card_type: string;
      bank: string;
      country_code: string;
      brand: string;
      reusable: boolean;
      signature: string;
      account_name: string | null;
    };
    customer: {
      id: number;
      first_name: string | null;
      last_name: string | null;
      email: string;
      customer_code: string;
      phone: string | null;
      metadata: any;
      risk_action: string;
      international_format_phone: string | null;
    };
    plan: any;
    split: any;
    order_id: any;
    paidAt: string;
    createdAt: string;
    requested_amount: number;
    pos_transaction_data: any;
    source: any;
    fees_breakdown_breakdown: any;
  };
}

// Paystack configuration
const paystackConfig: PaystackConfig = {
  publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || '',
  secretKey: process.env.PAYSTACK_SECRET_KEY || '',
  baseUrl: 'https://api.paystack.co'
};

// Generate unique payment reference
export function generatePaymentReference(prefix: string = 'CONTESTR'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${prefix}_${timestamp}_${random}`;
}

// Convert Naira to Kobo (Paystack uses kobo)
export function nairaToKobo(naira: number): number {
  return Math.round(naira * 100);
}

// Convert Kobo to Naira
export function koboToNaira(kobo: number): number {
  return kobo / 100;
}

// Format currency for display
export function formatNaira(amount: number): string {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

// Initialize Paystack payment
export async function initializePayment(paymentData: PaymentData): Promise<any> {
  try {
    const response = await fetch(`${paystackConfig.baseUrl}/transaction/initialize`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...paymentData,
        amount: nairaToKobo(paymentData.amount), // Convert to kobo
      }),
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Payment initialization failed');
    }

    return result;
  } catch (error) {
    console.error('Paystack initialization error:', error);
    throw error;
  }
}

// Verify payment
export async function verifyPayment(reference: string): Promise<PaymentVerificationResponse> {
  try {
    const response = await fetch(`${paystackConfig.baseUrl}/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Payment verification failed');
    }

    return result;
  } catch (error) {
    console.error('Paystack verification error:', error);
    throw error;
  }
}

// Create transfer recipient (for prize distribution)
export async function createTransferRecipient(
  name: string,
  accountNumber: string,
  bankCode: string,
  currency: string = 'NGN'
): Promise<any> {
  try {
    const response = await fetch(`${paystackConfig.baseUrl}/transferrecipient`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'nuban',
        name,
        account_number: accountNumber,
        bank_code: bankCode,
        currency,
      }),
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Transfer recipient creation failed');
    }

    return result;
  } catch (error) {
    console.error('Paystack transfer recipient error:', error);
    throw error;
  }
}

// Initiate transfer (for prize distribution)
export async function initiateTransfer(
  amount: number,
  recipientCode: string,
  reason: string,
  reference?: string
): Promise<any> {
  try {
    const response = await fetch(`${paystackConfig.baseUrl}/transfer`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source: 'balance',
        amount: nairaToKobo(amount),
        recipient: recipientCode,
        reason,
        reference: reference || generatePaymentReference('TRANSFER'),
      }),
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Transfer initiation failed');
    }

    return result;
  } catch (error) {
    console.error('Paystack transfer error:', error);
    throw error;
  }
}

// Get list of Nigerian banks
export async function getNigerianBanks(): Promise<any> {
  try {
    const response = await fetch(`${paystackConfig.baseUrl}/bank?currency=NGN`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${paystackConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Failed to fetch banks');
    }

    return result;
  } catch (error) {
    console.error('Paystack banks error:', error);
    throw error;
  }
}

// Verify bank account
export async function verifyBankAccount(
  accountNumber: string,
  bankCode: string
): Promise<any> {
  try {
    const response = await fetch(
      `${paystackConfig.baseUrl}/bank/resolve?account_number=${accountNumber}&bank_code=${bankCode}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${paystackConfig.secretKey}`,
          'Content-Type': 'application/json',
        },
      }
    );

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.message || 'Bank account verification failed');
    }

    return result;
  } catch (error) {
    console.error('Paystack bank verification error:', error);
    throw error;
  }
}

export { paystackConfig };
