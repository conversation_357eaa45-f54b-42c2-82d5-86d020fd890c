"use client";

import { motion } from "framer-motion";
import { Users, Trophy, Eye, Heart, Clock } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { SectionHeader, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card, { CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import VoteCounter from "@/components/shared/VoteCounter";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const FeaturedContestsSection: React.FC = () => {
  const contests = [
    {
      id: 1,
      title: "Face of Nigeria 2024",
      description: "Nigeria's premier beauty and talent contest celebrating the diversity, intelligence, and grace of Nigerian women.",
      category: "Beauty & Talent",
      participants: 234,
      votes: 1847,
      daysLeft: 12,
      prize: "₦2,000,000",
      image: "/api/placeholder/400/250",
      status: "active",
      organizer: "Miss Nigeria Organization",
      tags: ["Beauty", "Talent", "Nigeria"],
    },
    {
      id: 2,
      title: "Mr. Lagos 2024",
      description: "The ultimate male pageant celebrating the strength, intelligence, and charisma of Lagos men.",
      category: "Male Pageant",
      participants: 156,
      votes: 892,
      daysLeft: 8,
      prize: "₦1,000,000",
      image: "/api/placeholder/400/250",
      status: "active",
      organizer: "Lagos State Tourism Board",
      tags: ["Male", "Pageant", "Lagos"],
    },
    {
      id: 3,
      title: "Miss University Nigeria 2024",
      description: "A prestigious beauty pageant for female university students across Nigeria, celebrating academic excellence and beauty.",
      category: "Student Pageant",
      participants: 445,
      votes: 3241,
      daysLeft: 5,
      prize: "₦600,000",
      image: "/api/placeholder/400/250",
      status: "ending-soon",
      organizer: "Nigerian Universities Commission",
      tags: ["University", "Student", "Beauty"],
    },
  ];

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "ending-soon":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case "active":
        return "Active";
      case "ending-soon":
        return "Ending Soon";
      default:
        return "Closed";
    }
  };

  return (
    <AnimatedSection
      variant="default"
      size="xl"
      animation="fadeIn"
      className="relative"
      data-aos="fade-up"
      data-aos-duration="800"
      data-aos-delay="200"
    >
      {/* Modern Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(37,99,235,0.05)_1px,transparent_0)] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(37,99,235,0.1)_1px,transparent_0)] bg-[size:50px_50px] animate-pulse-slow" />
        <div className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-r from-primary-500/8 to-accent-500/4 dark:from-primary-500/15 dark:to-accent-500/8 rounded-full blur-3xl animate-float" />
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-accent-500/8 to-primary-500/4 dark:from-accent-500/15 dark:to-primary-500/8 rounded-full blur-3xl animate-float" style={{ animationDelay: "2s" }} />
      </div>

      <div className="relative z-10">
        <SectionHeader>
          <SectionTitle gradient>Featured Contests</SectionTitle>
          <SectionSubtitle>
            Discover amazing contests happening right now. 
            Join thousands of participants competing for exciting prizes.
          </SectionSubtitle>
        </SectionHeader>

        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {contests.map((contest, index) => (
            <motion.div
              key={contest.id}
              variants={staggerItemVariants}
            >
              <Card
                variant="elevated"
                hover
                interactive
                glow={index % 2 === 0 ? "blue" : "amber"}
                className="h-full group overflow-hidden"
                motionProps={{
                  whileHover: { y: -8, scale: 1.02 },
                  transition: { type: "spring", stiffness: 300 }
                }}
              >
                {/* Contest Image */}
                <div className="relative h-56 bg-gradient-to-br from-light-100 to-light-200 dark:from-dark-200 dark:to-dark-300 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-accent-500/10 dark:from-primary-500/30 dark:to-accent-500/20" />
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(37,99,235,0.1)_0%,transparent_70%)]" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Trophy className="h-20 w-20 text-primary-600/80 group-hover:scale-110 group-hover:rotate-12 transition-all duration-300" />
                  </div>
                  
                  {/* Status Badge */}
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(contest.status)}`}>
                      {getStatusText(contest.status)}
                    </span>
                  </div>

                  {/* Vote Counter */}
                  <div className="absolute top-4 right-4">
                    <VoteCounter
                      initialCount={contest.votes}
                      variant="heart"
                      size="sm"
                      className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg"
                    />
                  </div>
                </div>

                <CardContent className="p-6">
                  {/* Category */}
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-primary-600 dark:text-primary-400">
                      {contest.category}
                    </span>
                    <span className="text-sm font-bold text-secondary-600 dark:text-secondary-400">
                      {contest.prize}
                    </span>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-text-light dark:text-text-dark mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {contest.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                    {contest.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {contest.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 text-xs rounded-md"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Users className="h-4 w-4 text-gray-500 mr-1" />
                        <span className="text-sm font-semibold text-text-light dark:text-text-dark">
                          {contest.participants}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">Participants</span>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Heart className="h-4 w-4 text-gray-500 mr-1" />
                        <span className="text-sm font-semibold text-text-light dark:text-text-dark">
                          {contest.votes}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">Votes</span>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Clock className="h-4 w-4 text-gray-500 mr-1" />
                        <span className="text-sm font-semibold text-text-light dark:text-text-dark">
                          {contest.daysLeft}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">Days left</span>
                    </div>
                  </div>

                  {/* Organizer */}
                  <div className="text-sm text-gray-500 mb-4">
                    by <span className="font-medium">{contest.organizer}</span>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-3">
                    <Button
                      variant="primary"
                      size="sm"
                      className="flex-1"
                      motionProps={{
                        whileHover: { scale: 1.02 },
                        whileTap: { scale: 0.98 }
                      }}
                    >
                      View Contest
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      leftIcon={<Eye className="h-4 w-4" />}
                      motionProps={{
                        whileHover: { scale: 1.02 },
                        whileTap: { scale: 0.98 }
                      }}
                    >
                      Preview
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Button
            variant="outline"
            size="lg"
            className="px-8"
            motionProps={{
              whileHover: { scale: 1.05 },
              whileTap: { scale: 0.95 }
            }}
          >
            View All Contests
          </Button>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default FeaturedContestsSection;
