"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Trophy,
  Users,
  DollarSign,
  TrendingUp,
  Eye,
  Calendar,
  Award,
  Target,
  Plus,
  BarChart3,
  Clock,
  CheckCircle
} from "lucide-react";
import { useRouter } from "next/navigation";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { ContestsService } from "@/lib/api/contests";
import { ContestantsService } from "@/lib/api/contestants";

interface StatCard {
  title: string;
  value: string;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  icon: any;
  color: string;
}

interface Contest {
  id: string;
  title: string;
  status: "active" | "draft" | "completed" | "scheduled";
  participants: number;
  votes: number;
  endDate: string;
  prize: string;
}

interface RecentActivity {
  id: string;
  type: "participant" | "vote" | "contest" | "payment";
  title: string;
  description: string;
  time: string;
  status: "success" | "warning" | "error" | "info";
}

const statsCards: StatCard[] = [
  {
    title: "Active Contests",
    value: "5",
    change: "+2",
    changeType: "positive",
    icon: Trophy,
    color: "text-yellow-600 dark:text-yellow-400"
  },
  {
    title: "Total Participants",
    value: "1,247",
    change: "+18%",
    changeType: "positive",
    icon: Users,
    color: "text-blue-600 dark:text-blue-400"
  },
  {
    title: "Total Votes",
    value: "8,934",
    change: "+25%",
    changeType: "positive",
    icon: Target,
    color: "text-green-600 dark:text-green-400"
  },
  {
    title: "Contest Revenue",
    value: "$12,450",
    change: "+15%",
    changeType: "positive",
    icon: DollarSign,
    color: "text-purple-600 dark:text-purple-400"
  }
];

const recentContests: Contest[] = [
  {
    id: "1",
    title: "Best Innovation Award 2024",
    status: "active",
    participants: 156,
    votes: 1234,
    endDate: "2024-12-31",
    prize: "$5,000"
  },
  {
    id: "2",
    title: "Creative Design Challenge",
    status: "active",
    participants: 89,
    votes: 567,
    endDate: "2024-12-25",
    prize: "$2,500"
  },
  {
    id: "3",
    title: "Tech Startup Pitch",
    status: "scheduled",
    participants: 0,
    votes: 0,
    endDate: "2025-01-15",
    prize: "$10,000"
  },
  {
    id: "4",
    title: "Photography Contest",
    status: "completed",
    participants: 234,
    votes: 2156,
    endDate: "2024-11-30",
    prize: "$1,500"
  }
];

const recentActivities: RecentActivity[] = [
  {
    id: "1",
    type: "participant",
    title: "New Participant Registered",
    description: "John Doe joined Best Innovation Award 2024",
    time: "5 minutes ago",
    status: "success"
  },
  {
    id: "2",
    type: "vote",
    title: "Vote Cast",
    description: "Sarah voted in Creative Design Challenge",
    time: "12 minutes ago",
    status: "info"
  },
  {
    id: "3",
    type: "contest",
    title: "Contest Updated",
    description: "Photography Contest rules modified",
    time: "1 hour ago",
    status: "warning"
  },
  {
    id: "4",
    type: "payment",
    title: "Payment Received",
    description: "$150 contest fee payment processed",
    time: "2 hours ago",
    status: "success"
  }
];

export default function VendorDashboard() {
  const router = useRouter();
  const [contests, setContests] = useState<Contest[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalContests: 0,
    totalParticipants: 0,
    totalVotes: 0,
    totalRevenue: 0
  });

  // Fetch vendor's contests and statistics
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch vendor's contests
        const contestsResponse = await ContestsService.getClientContests();

        if (contestsResponse.success && contestsResponse.contests) {
          const transformedContests: Contest[] = contestsResponse.contests.map(contest => ({
            id: contest._id,
            title: contest.title,
            status: contest.status as "active" | "draft" | "completed" | "scheduled",
            participants: 0, // Will be fetched separately
            votes: 0, // Will be fetched separately
            endDate: contest.endsAt,
            prize: `₦${contest.registrationFee?.toLocaleString() || '0'}`
          }));

          setContests(transformedContests);

          // Calculate basic stats
          setStats({
            totalContests: transformedContests.length,
            totalParticipants: 0, // Will be calculated from contestants
            totalVotes: 0, // Will be calculated from voting
            totalRevenue: contestsResponse.contests.reduce((sum, contest) => sum + (contest.registrationFee || 0), 0)
          });
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Fallback to mock data
        setContests(mockContests);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400";
      case "scheduled":
        return "bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400";
      case "completed":
        return "bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400";
      case "draft":
        return "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400";
      default:
        return "bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400";
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "participant":
        return <Users className="h-4 w-4" />;
      case "vote":
        return <Target className="h-4 w-4" />;
      case "contest":
        return <Trophy className="h-4 w-4" />;
      case "payment":
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20";
      case "warning":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20";
      case "error":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20";
      default:
        return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20";
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Vendor Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Welcome back! Here's an overview of your contests and activities.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Analytics
          </Button>
          <Button
            variant="gradient"
            size="sm"
            glow={true}
            onClick={() => router.push('/vendor/create-contest')}
            motionProps={{
              whileHover: { scale: 1.05, y: -2 },
              whileTap: { scale: 0.95 }
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Contest
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: "Total Contests",
            value: stats.totalContests.toString(),
            change: "+12%",
            changeType: "positive" as const,
            icon: Trophy,
            color: "from-blue-500 to-blue-600"
          },
          {
            title: "Total Participants",
            value: stats.totalParticipants.toString(),
            change: "+8%",
            changeType: "positive" as const,
            icon: Users,
            color: "from-green-500 to-green-600"
          },
          {
            title: "Total Votes",
            value: stats.totalVotes.toString(),
            change: "+23%",
            changeType: "positive" as const,
            icon: Target,
            color: "from-purple-500 to-purple-600"
          },
          {
            title: "Revenue",
            value: `₦${stats.totalRevenue.toLocaleString()}`,
            change: "+15%",
            changeType: "positive" as const,
            icon: DollarSign,
            color: "from-orange-500 to-orange-600"
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
                    {stat.value}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className={`text-sm font-medium ${
                      stat.changeType === "positive" 
                        ? "text-green-600 dark:text-green-400" 
                        : stat.changeType === "negative"
                        ? "text-red-600 dark:text-red-400"
                        : "text-gray-600 dark:text-gray-400"
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-500 ml-1">
                      from last month
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-100 dark:bg-gray-800 ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Contests */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Recent Contests
              </h2>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 animate-pulse">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-3"></div>
                      <div className="grid grid-cols-4 gap-4">
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : contests.length === 0 ? (
                <div className="text-center py-8">
                  <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No contests yet
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    Create your first contest to get started
                  </p>
                  <Button
                    variant="gradient"
                    onClick={() => router.push('/vendor/create-contest')}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Contest
                  </Button>
                </div>
              ) : (
                contests.slice(0, 3).map((contest) => (
                <div key={contest.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {contest.title}
                    </h3>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(contest.status)}`}>
                      {contest.status.charAt(0).toUpperCase() + contest.status.slice(1)}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500 dark:text-gray-400">Participants</p>
                      <p className="font-medium text-gray-900 dark:text-white">{contest.participants}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 dark:text-gray-400">Votes</p>
                      <p className="font-medium text-gray-900 dark:text-white">{contest.votes}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 dark:text-gray-400">Prize</p>
                      <p className="font-medium text-gray-900 dark:text-white">{contest.prize}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 dark:text-gray-400">End Date</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {new Date(contest.endDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 mt-4">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    {contest.status === "active" && (
                      <Button variant="ghost" size="sm">
                        Manage
                      </Button>
                    )}
                  </div>
                </div>
                ))
              )}
            </div>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Recent Activity
              </h2>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${getStatusColor(activity.status)}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {activity.title}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="p-4 h-auto justify-start"
            onClick={() => router.push('/vendor/create-contest')}
            motionProps={{
              whileHover: { scale: 1.02 },
              whileTap: { scale: 0.98 }
            }}
          >
            <Plus className="h-5 w-5 mr-3 text-blue-600 dark:text-blue-400" />
            <div className="text-left">
              <div className="font-medium">Create New Contest</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Start a new competition</div>
            </div>
          </Button>
          <Button variant="outline" className="p-4 h-auto justify-start">
            <Users className="h-5 w-5 mr-3 text-green-600 dark:text-green-400" />
            <div className="text-left">
              <div className="font-medium">Manage Participants</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">View and manage entries</div>
            </div>
          </Button>
          <Button variant="outline" className="p-4 h-auto justify-start">
            <BarChart3 className="h-5 w-5 mr-3 text-purple-600 dark:text-purple-400" />
            <div className="text-left">
              <div className="font-medium">View Analytics</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Contest performance data</div>
            </div>
          </Button>
          <Button variant="outline" className="p-4 h-auto justify-start">
            <Award className="h-5 w-5 mr-3 text-yellow-600 dark:text-yellow-400" />
            <div className="text-left">
              <div className="font-medium">Award Prizes</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Distribute contest rewards</div>
            </div>
          </Button>
        </div>
      </Card>
    </div>
  );
}
