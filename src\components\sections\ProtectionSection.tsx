"use client";

import { motion } from "framer-motion";
import { Shield, Lock, Eye, CheckCircle, AlertTriangle } from "lucide-react";
import AnimatedSection from "@/components/ui/AnimatedSection";
import { <PERSON><PERSON><PERSON>er, SectionTitle, SectionSubtitle } from "@/components/ui/Section";
import Card from "@/components/ui/Card";
import { staggerContainerVariants, staggerItemVariants } from "@/utils/framerVariants";

const ProtectionSection: React.FC = () => {
  const protections = [
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level encryption and security protocols protect all data and transactions.",
      features: ["256-bit SSL encryption", "SOC 2 compliance", "Regular security audits", "GDPR compliant"],
    },
    {
      icon: Lock,
      title: "Fraud Prevention",
      description: "Advanced algorithms detect and prevent fraudulent voting and manipulation.",
      features: ["IP tracking", "Device fingerprinting", "Behavioral analysis", "Real-time monitoring"],
    },
    {
      icon: Eye,
      title: "Transparent Process",
      description: "Complete audit trails and transparency ensure fair and trustworthy contests.",
      features: ["Vote verification", "Public audit logs", "Real-time monitoring", "Dispute resolution"],
    },
  ];

  const certifications = [
    { name: "SOC 2 Type II", icon: Shield },
    { name: "GDPR Compliant", icon: Lock },
    { name: "ISO 27001", icon: CheckCircle },
    { name: "PCI DSS", icon: AlertTriangle },
  ];

  const stats = [
    { value: "99.9%", label: "Uptime Guarantee" },
    { value: "0", label: "Security Breaches" },
    { value: "24/7", label: "Monitoring" },
    { value: "< 1s", label: "Response Time" },
  ];

  return (
    <AnimatedSection
      variant="dark"
      size="xl"
      animation="fadeIn"
      className="relative overflow-hidden"
    >
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-primary-900 to-gray-900" />
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.3)_1px,transparent_0)] bg-[size:50px_50px]" />
        </div>
      </div>

      <div className="relative z-10">
        <SectionHeader>
          <SectionTitle className="text-white">
            <Shield className="inline-block h-12 w-12 mr-4 text-secondary-400" />
            Security & Protection
          </SectionTitle>
          <SectionSubtitle className="text-white/90">
            Your contests are protected by enterprise-grade security measures and 
            transparent processes that ensure fairness and trust.
          </SectionSubtitle>
        </SectionHeader>

        {/* Main Protection Features */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {protections.map((protection, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
            >
              <Card
                hover
                className="h-full bg-white/5 backdrop-blur-sm border-white/10 group"
                motionProps={{
                  whileHover: { y: -8, scale: 1.02 },
                  transition: { type: "spring", stiffness: 300 }
                }}
              >
                {/* Icon */}
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-secondary-500 to-primary-500 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                  <protection.icon className="h-8 w-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-white mb-4">
                  {protection.title}
                </h3>
                
                <p className="text-white/80 mb-6 leading-relaxed">
                  {protection.description}
                </p>

                {/* Features */}
                <ul className="space-y-3">
                  {protection.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center text-sm text-white/70"
                    >
                      <CheckCircle className="h-4 w-4 text-secondary-400 mr-3 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              variants={staggerItemVariants}
              className="text-center"
            >
              <div className="text-4xl lg:text-5xl font-bold text-white mb-2">
                {stat.value}
              </div>
              <div className="text-white/70">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Certifications */}
        <motion.div
          variants={staggerContainerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          className="text-center"
        >
          <motion.h3
            variants={staggerItemVariants}
            className="text-2xl font-bold text-white mb-8"
          >
            Trusted & Certified
          </motion.h3>
          
          <motion.div
            variants={staggerItemVariants}
            className="flex flex-wrap justify-center items-center gap-8"
          >
            {certifications.map((cert, index) => (
              <motion.div
                key={index}
                className="flex items-center space-x-3 text-white/80 hover:text-white transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
              >
                <cert.icon className="h-6 w-6" />
                <span className="font-medium">{cert.name}</span>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </AnimatedSection>
  );
};

export default ProtectionSection;
