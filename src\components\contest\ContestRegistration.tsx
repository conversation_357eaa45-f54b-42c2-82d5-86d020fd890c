"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  CreditCard,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  FileText,
  Camera
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import usePaystack from '@/hooks/usePaystack';
import { ContestantsService } from '@/lib/api/contestants';

interface Contest {
  id: string;
  title: string;
  entryFee?: string;
  requirements: string[];
  deadline: string;
}

interface ContestRegistrationProps {
  contest: Contest;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (registrationData: any) => void;
}

interface RegistrationFormData {
  name: string;
  stageName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  address: string;
  city: string;
  state: string;
  bio: string;
  socialMedia: {
    instagram: string;
    twitter: string;
    facebook: string;
  };
  agreeToTerms: boolean;
  agreeToRequirements: boolean;
  profilePhoto?: File;
}

const initialFormData: RegistrationFormData = {
  name: '',
  stageName: '',
  email: '',
  phone: '',
  dateOfBirth: '',
  address: '',
  city: '',
  state: '',
  bio: '',
  socialMedia: {
    instagram: '',
    twitter: '',
    facebook: ''
  },
  agreeToTerms: false,
  agreeToRequirements: false,
  profilePhoto: undefined
};

const nigerianStates = [
  'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',
  'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe',
  'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara',
  'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau',
  'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
];

export default function ContestRegistration({
  contest,
  isOpen,
  onClose,
  onSuccess
}: ContestRegistrationProps) {
  const [formData, setFormData] = useState<RegistrationFormData>(initialFormData);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [registrationComplete, setRegistrationComplete] = useState(false);

  const { initializeContestPayment, isLoading: paymentLoading, error: paymentError } = usePaystack({
    onSuccess: (reference, transaction) => {
      handleRegistrationSuccess(reference, transaction);
    },
    onError: (error) => {
      console.error('Payment failed:', error);
    },
    onClose: () => {
      console.log('Payment popup closed');
    }
  });

  const updateFormData = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof RegistrationFormData],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.name && formData.stageName && formData.email && formData.phone);
      case 2:
        return !!(formData.dateOfBirth && formData.address && formData.city && formData.state);
      case 3:
        return !!(formData.bio && formData.agreeToTerms && formData.agreeToRequirements);
      default:
        return false;
    }
  };

  const handleRegistrationSuccess = async (reference: string, transaction: any) => {
    setIsSubmitting(true);

    try {
      // Create contestant using API
      const contestantData = {
        name: formData.name,
        stageName: formData.stageName,
        email: formData.email,
        phone: formData.phone,
        contestID: contest.id,
        bio: formData.bio,
        paymentReference: reference,
        contestantphoto: formData.profilePhoto
      };

      const response = await ContestantsService.createContestant(contestantData);

      if (response.success) {
        setRegistrationComplete(true);
        onSuccess?.(response.contestant);
      } else {
        throw new Error(response.message || 'Registration failed');
      }

    } catch (error) {
      console.error('Registration save failed:', error);
      // Show error to user
      alert('Registration failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateStep(3)) return;

    const entryFee = contest.entryFee ? parseFloat(contest.entryFee.replace(/[₦,]/g, '')) : 0;

    if (entryFee > 0) {
      // Process payment
      await initializeContestPayment(
        contest.id,
        formData.email,
        entryFee,
        contest.title,
        `${formData.name} ${formData.stageName}`
      );
    } else {
      // Free registration
      await handleRegistrationSuccess('FREE_REGISTRATION', { amount: 0 });
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep) && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isOpen) return null;

  if (registrationComplete) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white dark:bg-gray-800 rounded-xl max-w-md w-full p-8 text-center"
        >
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Registration Successful!
          </h3>
          
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You have successfully registered for {contest.title}. Good luck!
          </p>
          
          <Button
            variant="gradient"
            onClick={onClose}
            glow={true}
            fullWidth
          >
            Continue
          </Button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                Register for {contest.title}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Step {currentStep} of 3
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              leftIcon={<X className="w-4 h-4" />}
            >
              Close
            </Button>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-500'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`w-16 h-1 mx-2 ${
                      currentStep > step ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {currentStep === 1 && (
            <PersonalInfoStep formData={formData} updateFormData={updateFormData} />
          )}

          {currentStep === 2 && (
            <AddressInfoStep formData={formData} updateFormData={updateFormData} />
          )}

          {currentStep === 3 && (
            <FinalStep
              formData={formData}
              updateFormData={updateFormData}
              contest={contest}
            />
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            Previous
          </Button>

          {currentStep < 3 ? (
            <Button
              variant="gradient"
              onClick={nextStep}
              disabled={!validateStep(currentStep)}
              glow={validateStep(currentStep)}
            >
              Next
            </Button>
          ) : (
            <Button
              variant="gradient"
              onClick={handleSubmit}
              disabled={!validateStep(3) || paymentLoading || isSubmitting}
              isLoading={paymentLoading || isSubmitting}
              loadingText={paymentLoading ? "Processing Payment..." : "Completing Registration..."}
              glow={validateStep(3)}
              leftIcon={contest.entryFee ? <CreditCard className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
            >
              {contest.entryFee ? `Pay ${contest.entryFee} & Register` : 'Complete Registration'}
            </Button>
          )}
        </div>
        
        {paymentError && (
          <div className="px-6 pb-4">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800 dark:text-red-200">Payment Error</p>
                <p className="text-sm text-red-600 dark:text-red-300">{paymentError}</p>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}

// Step Components
function PersonalInfoStep({
  formData,
  updateFormData
}: {
  formData: RegistrationFormData;
  updateFormData: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Personal Information
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please provide your basic personal details
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          label="Full Name"
          placeholder="Enter your full name"
          value={formData.name}
          onChange={(e) => updateFormData('name', e.target.value)}
          leftIcon={<User className="w-4 h-4" />}
          required
        />

        <Input
          label="Stage Name"
          placeholder="Enter your stage/display name"
          value={formData.stageName}
          onChange={(e) => updateFormData('stageName', e.target.value)}
          leftIcon={<User className="w-4 h-4" />}
          required
        />

        <Input
          label="Email Address"
          type="email"
          placeholder="Enter your email"
          value={formData.email}
          onChange={(e) => updateFormData('email', e.target.value)}
          leftIcon={<Mail className="w-4 h-4" />}
          required
        />

        <Input
          label="Phone Number"
          placeholder="Enter your phone number"
          value={formData.phone}
          onChange={(e) => updateFormData('phone', e.target.value)}
          leftIcon={<Phone className="w-4 h-4" />}
          required
        />
      </div>

      {/* Profile Photo Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Profile Photo (Optional)
        </label>
        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
          <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Upload your profile photo
          </p>
          <input
            type="file"
            accept="image/*"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                updateFormData('profilePhoto', file);
              }
            }}
            className="hidden"
            id="profile-photo"
          />
          <label
            htmlFor="profile-photo"
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          >
            Choose File
          </label>
          {formData.profilePhoto && (
            <p className="text-sm text-green-600 dark:text-green-400 mt-2">
              {formData.profilePhoto.name}
            </p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Input
          label="Instagram Handle"
          placeholder="@username"
          value={formData.socialMedia.instagram}
          onChange={(e) => updateFormData('socialMedia.instagram', e.target.value)}
        />

        <Input
          label="Twitter Handle"
          placeholder="@username"
          value={formData.socialMedia.twitter}
          onChange={(e) => updateFormData('socialMedia.twitter', e.target.value)}
        />

        <Input
          label="Facebook Profile"
          placeholder="Profile name"
          value={formData.socialMedia.facebook}
          onChange={(e) => updateFormData('socialMedia.facebook', e.target.value)}
        />
      </div>
    </div>
  );
}

function AddressInfoStep({
  formData,
  updateFormData
}: {
  formData: RegistrationFormData;
  updateFormData: (field: string, value: any) => void;
}) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Address & Personal Details
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please provide your address and additional details
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          label="Date of Birth"
          type="date"
          value={formData.dateOfBirth}
          onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
          leftIcon={<Calendar className="w-4 h-4" />}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            State
          </label>
          <select
            value={formData.state}
            onChange={(e) => updateFormData('state', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            required
          >
            <option value="">Select your state</option>
            {nigerianStates.map(state => (
              <option key={state} value={state}>{state}</option>
            ))}
          </select>
        </div>

        <Input
          label="City"
          placeholder="Enter your city"
          value={formData.city}
          onChange={(e) => updateFormData('city', e.target.value)}
          leftIcon={<MapPin className="w-4 h-4" />}
          required
        />

        <div className="md:col-span-1">
          <Input
            label="Address"
            placeholder="Enter your full address"
            value={formData.address}
            onChange={(e) => updateFormData('address', e.target.value)}
            leftIcon={<MapPin className="w-4 h-4" />}
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Bio / About Yourself
        </label>
        <textarea
          value={formData.bio}
          onChange={(e) => updateFormData('bio', e.target.value)}
          placeholder="Tell us about yourself, your interests, achievements, and why you want to participate in this contest..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          required
        />
      </div>
    </div>
  );
}

function FinalStep({
  formData,
  updateFormData,
  contest
}: {
  formData: RegistrationFormData;
  updateFormData: (field: string, value: any) => void;
  contest: Contest;
}) {
  const entryFee = contest.entryFee ? parseFloat(contest.entryFee.replace(/[₦,]/g, '')) : 0;

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Review & Confirm
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Please review your information and agree to the terms
        </p>
      </div>

      {/* Registration Summary */}
      <Card variant="elevated">
        <div className="p-4">
          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
            Registration Summary
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Name:</span>
              <span className="text-gray-900 dark:text-gray-100">
                {formData.name}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Stage Name:</span>
              <span className="text-gray-900 dark:text-gray-100">
                {formData.stageName}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Email:</span>
              <span className="text-gray-900 dark:text-gray-100">{formData.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Location:</span>
              <span className="text-gray-900 dark:text-gray-100">
                {formData.city}, {formData.state}
              </span>
            </div>
            <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
              <span className="text-gray-600 dark:text-gray-400">Entry Fee:</span>
              <span className="font-semibold text-gray-900 dark:text-gray-100">
                {entryFee > 0 ? contest.entryFee : 'Free'}
              </span>
            </div>
          </div>
        </div>
      </Card>

      {/* Contest Requirements */}
      <Card variant="elevated">
        <div className="p-4">
          <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
            Contest Requirements
          </h4>
          <div className="space-y-2">
            {contest.requirements.map((requirement, index) => (
              <div key={index} className="flex items-start text-sm">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-gray-600 dark:text-gray-400">{requirement}</span>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Agreement Checkboxes */}
      <div className="space-y-4">
        <label className="flex items-start">
          <input
            type="checkbox"
            checked={formData.agreeToRequirements}
            onChange={(e) => updateFormData('agreeToRequirements', e.target.checked)}
            className="mt-1 mr-3 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            I confirm that I meet all the contest requirements listed above
          </span>
        </label>

        <label className="flex items-start">
          <input
            type="checkbox"
            checked={formData.agreeToTerms}
            onChange={(e) => updateFormData('agreeToTerms', e.target.checked)}
            className="mt-1 mr-3 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            I agree to the{' '}
            <a href="#" className="text-primary-600 hover:text-primary-700 underline">
              Terms and Conditions
            </a>{' '}
            and{' '}
            <a href="#" className="text-primary-600 hover:text-primary-700 underline">
              Privacy Policy
            </a>
          </span>
        </label>
      </div>

      {entryFee > 0 && isLoggedIn && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-start">
            <CreditCard className="w-5 h-5 text-blue-500 mr-3 mt-0.5" />
            <div>
              <h5 className="font-medium text-blue-900 dark:text-blue-100">Payment Information</h5>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                You will be redirected to Paystack to securely complete your payment of {contest.entryFee}.
                Your registration will be confirmed once payment is successful.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
