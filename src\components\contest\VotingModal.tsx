"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Heart,
  CreditCard,
  User,
  Trophy,
  X,
  Plus,
  Minus,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import usePaystack from '@/hooks/usePaystack';
import { formatNaira } from '@/lib/paystack';

interface Contestant {
  id: string;
  name: string;
  age: number;
  location: string;
  bio: string;
  votes: number;
  image: string;
  registeredAt: string;
  featured: boolean;
}

interface Contest {
  id: string;
  title: string;
  votePrice: string;
}

interface VotingModalProps {
  contestant: Contestant;
  contest: Contest;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (voteData: any) => void;
  isLoggedIn?: boolean;
  onLoginRequired?: () => void;
}

export default function VotingModal({
  contestant,
  contest,
  isOpen,
  onClose,
  onSuccess,
  isLoggedIn = false,
  onLoginRequired
}: VotingModalProps) {
  const [voteQuantity, setVoteQuantity] = useState(1);
  const [voterEmail, setVoterEmail] = useState('');
  const [voterName, setVoterName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [voteComplete, setVoteComplete] = useState(false);

  const votePrice = parseFloat(contest.votePrice || '0');
  const totalAmount = voteQuantity * votePrice;
  const isFreeVoting = votePrice === 0;

  const { initializeContestPayment, isLoading: paymentLoading, error: paymentError } = usePaystack({
    onSuccess: (reference, transaction) => {
      handleVoteSuccess(reference, transaction);
    },
    onError: (error) => {
      console.error('Vote payment failed:', error);
    },
    onClose: () => {
      console.log('Vote payment popup closed');
    }
  });

  const handleVoteSuccess = async (reference: string, transaction: any) => {
    setIsSubmitting(true);
    
    try {
      // Here you would typically save the vote data to your backend
      const voteData = {
        contestantId: contestant.id,
        contestId: contest.id,
        voteQuantity,
        totalAmount,
        paymentReference: reference,
        voterEmail: isLoggedIn ? undefined : voterEmail,
        voterName: isLoggedIn ? undefined : voterName,
        voteDate: new Date().toISOString()
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setVoteComplete(true);
      onSuccess?.(voteData);
      
    } catch (error) {
      console.error('Vote save failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVoteSubmit = async () => {
    // Check if user is logged in for paid voting
    if (!isFreeVoting && !isLoggedIn) {
      onLoginRequired?.();
      return;
    }

    // Validate anonymous voter details for paid voting
    if (!isFreeVoting && !isLoggedIn && (!voterEmail || !voterName)) {
      return;
    }

    if (isFreeVoting) {
      // Free voting - no payment required
      await handleVoteSuccess('FREE_VOTE', { amount: 0 });
    } else {
      // Paid voting - process payment
      const email = isLoggedIn ? '<EMAIL>' : voterEmail; // Replace with actual user email
      const name = isLoggedIn ? 'Current User' : voterName; // Replace with actual user name
      
      await initializeContestPayment(
        contest.id,
        email,
        totalAmount,
        `${voteQuantity} vote${voteQuantity > 1 ? 's' : ''} for ${contestant.name}`,
        name
      );
    }
  };

  const incrementVotes = () => {
    setVoteQuantity(prev => Math.min(prev + 1, 100)); // Max 100 votes per transaction
  };

  const decrementVotes = () => {
    setVoteQuantity(prev => Math.max(prev - 1, 1));
  };

  if (!isOpen) return null;

  if (voteComplete) {
    return (
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-2xl max-w-lg w-full p-8 text-center shadow-2xl border border-gray-200 dark:border-gray-700"
        >
          <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>

          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
            Vote{voteQuantity > 1 ? 's' : ''} Cast Successfully!
          </h3>

          <p className="text-gray-600 dark:text-gray-400 mb-2 text-lg">
            You have successfully cast <span className="font-semibold text-primary-600 dark:text-primary-400">{voteQuantity} vote{voteQuantity > 1 ? 's' : ''}</span> for <span className="font-semibold">{contestant.name}</span>
            {!isFreeVoting && (
              <>
                <br />
                <span className="text-sm">Total amount: <span className="font-semibold text-green-600">{formatNaira(totalAmount)}</span></span>
              </>
            )}.
          </p>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Thank you for supporting {contestant.name}! Your vote{voteQuantity > 1 ? 's have' : ' has'} been recorded and will help them in the competition.
            </p>
          </div>

          <Button
            variant="gradient"
            size="lg"
            onClick={onClose}
            glow={true}
            fullWidth
            leftIcon={<Heart className="w-5 h-5" />}
          >
            Continue Voting
          </Button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: 20 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
        className="bg-white dark:bg-gray-800 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-gray-200 dark:border-gray-700"
      >
        {/* Header */}
        <div className="p-8 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  Vote for {contestant.name}
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 flex items-center">
                  <Trophy className="w-4 h-4 mr-1" />
                  {contest.title}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          {/* Contestant Info */}
          <Card variant="elevated" className="mb-8">
            <div className="p-6">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-gray-700 dark:to-gray-600 rounded-xl flex items-center justify-center shadow-lg">
                  <User className="w-10 h-10 text-primary-600 dark:text-gray-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {contestant.name}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <span className="flex items-center">
                      <User className="w-4 h-4 mr-1" />
                      Age {contestant.age}
                    </span>
                    <span className="flex items-center">
                      <User className="w-4 h-4 mr-1" />
                      {contestant.location}
                    </span>
                  </div>
                  <div className="flex items-center gap-6">
                    <div className="text-center">
                      <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
                        {contestant.votes.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">Current Votes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-secondary-600 dark:text-secondary-400">
                        #{contestant.featured ? '⭐' : ''}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {contestant.featured ? 'Featured' : 'Contestant'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Vote Quantity Selector */}
          <div className="mb-8">
            <label className="block text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              How many votes would you like to cast?
            </label>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
              <div className="flex items-center justify-center gap-6 mb-4">
                <Button
                  variant="outline"
                  size="lg"
                  onClick={decrementVotes}
                  disabled={voteQuantity <= 1}
                  className="w-12 h-12 rounded-full"
                >
                  <Minus className="w-5 h-5" />
                </Button>

                <div className="text-center px-4">
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={voteQuantity}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      setVoteQuantity(Math.min(Math.max(value, 1), 100));
                    }}
                    className="text-5xl font-bold text-gray-900 dark:text-gray-100 bg-transparent text-center w-32 border-none outline-none focus:ring-2 focus:ring-primary-500 rounded-lg"
                  />
                  <div className="text-lg text-gray-600 dark:text-gray-400 mt-2">
                    vote{voteQuantity > 1 ? 's' : ''}
                  </div>
                </div>

                <Button
                  variant="outline"
                  size="lg"
                  onClick={incrementVotes}
                  disabled={voteQuantity >= 100}
                  className="w-12 h-12 rounded-full"
                >
                  <Plus className="w-5 h-5" />
                </Button>
              </div>

              <div className="text-center">
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                  You can cast up to 100 votes per transaction
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500">
                  Click the number above to type directly
                </p>
              </div>
            </div>
          </div>

          {/* Cost Display */}
          <Card variant="elevated" className="mb-8">
            <div className="p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Payment Summary
              </h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">
                    {voteQuantity} vote{voteQuantity > 1 ? 's' : ''} × {isFreeVoting ? 'Free' : formatNaira(votePrice)}
                  </span>
                  <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {isFreeVoting ? 'Free' : formatNaira(totalAmount)}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Total Amount
                    </span>
                    <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                      {isFreeVoting ? 'Free' : formatNaira(totalAmount)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Anonymous Voter Details (for paid voting when not logged in) */}
          {!isFreeVoting && !isLoggedIn && (
            <Card variant="elevated" className="mb-8">
              <div className="p-6">
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Voter Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Your Name"
                    placeholder="Enter your full name"
                    value={voterName}
                    onChange={(e) => setVoterName(e.target.value)}
                    required
                  />
                  <Input
                    label="Your Email"
                    type="email"
                    placeholder="Enter your email address"
                    value={voterEmail}
                    onChange={(e) => setVoterEmail(e.target.value)}
                    required
                  />
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-3">
                  This information is required for payment processing and vote verification.
                </p>
              </div>
            </Card>
          )}

          {/* Login Prompt for Paid Voting */}
          {!isFreeVoting && !isLoggedIn && (
            <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start">
                <User className="w-5 h-5 text-blue-500 mr-3 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900 dark:text-blue-100">
                    Have an account?
                  </h5>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    <button
                      onClick={onLoginRequired}
                      className="underline hover:no-underline"
                    >
                      Login here
                    </button> for faster voting and to track your vote history.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-8 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              variant="outline"
              size="lg"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>

            <Button
              variant="gradient"
              size="lg"
              onClick={handleVoteSubmit}
              disabled={
                paymentLoading ||
                isSubmitting ||
                (!isFreeVoting && !isLoggedIn && (!voterEmail || !voterName))
              }
              isLoading={paymentLoading || isSubmitting}
              loadingText={paymentLoading ? "Processing Payment..." : "Casting Votes..."}
              glow={true}
              leftIcon={isFreeVoting ? <Heart className="w-5 h-5" /> : <CreditCard className="w-5 h-5" />}
              className="flex-2 min-w-[200px]"
            >
              {isFreeVoting
                ? `Cast ${voteQuantity} Vote${voteQuantity > 1 ? 's' : ''}`
                : `Pay ${formatNaira(totalAmount)} & Vote`
              }
            </Button>
          </div>

          {paymentError && (
            <div className="mt-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-start">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800 dark:text-red-200">Payment Error</p>
                <p className="text-sm text-red-600 dark:text-red-300">{paymentError}</p>
              </div>
            </div>
          )}

          {!isFreeVoting && (
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Secure payment powered by Paystack • Your payment information is protected
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
